[2025-06-19 02:03:04] local.ERROR: Method Illuminate\Http\Request::validated does not exist. {"userId":2,"exception":"[object] (BadMethodCallException(code: 0): Method Illuminate\\Http\\Request::validated does not exist. at /var/www/vendor/laravel/framework/src/Illuminate/Macroable/Traits/Macroable.php:115)
[stacktrace]
#0 /var/www/app/Http/Controllers/Api/V1/InvitationController.php(73): Illuminate\\Http\\Request->__call('validated', Array)
#1 /var/www/vendor/laravel/framework/src/Illuminate/Routing/Controller.php(54): App\\Http\\Controllers\\Api\\V1\\InvitationController->store(Object(Illuminate\\Http\\Request))
#2 /var/www/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('store', Array)
#3 /var/www/vendor/laravel/framework/src/Illuminate/Routing/Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Api\\V1\\InvitationController), 'store')
#4 /var/www/vendor/laravel/framework/src/Illuminate/Routing/Route.php(211): Illuminate\\Routing\\Route->runController()
#5 /var/www/vendor/laravel/framework/src/Illuminate/Routing/Router.php(808): Illuminate\\Routing\\Route->run()
#6 /var/www/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}(Object(Illuminate\\Http\\Request))
#7 /var/www/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authorize.php(59): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#8 /var/www/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authorize->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'create', 'App\\\\Models\\\\Invi...')
#9 /var/www/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#10 /var/www/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#11 /var/www/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#12 /var/www/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'sanctum')
#13 /var/www/vendor/laravel/sanctum/src/Http/Middleware/EnsureFrontendRequestsAreStateful.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#14 /var/www/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful->{closure:Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful::handle():25}(Object(Illuminate\\Http\\Request))
#15 /var/www/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#16 /var/www/vendor/laravel/sanctum/src/Http/Middleware/EnsureFrontendRequestsAreStateful.php(25): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#17 /var/www/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 /var/www/app/Http/Middleware/ApiMiddleware.php(29): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#19 /var/www/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): App\\Http\\Middleware\\ApiMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 /var/www/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#21 /var/www/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#22 /var/www/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#23 /var/www/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#24 /var/www/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#25 /var/www/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#26 /var/www/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#27 /var/www/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#28 /var/www/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 /var/www/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 /var/www/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#31 /var/www/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 /var/www/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 /var/www/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#34 /var/www/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 /var/www/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#36 /var/www/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 /var/www/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#38 /var/www/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 /var/www/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#40 /var/www/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 /var/www/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#42 /var/www/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 /var/www/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#44 /var/www/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 /var/www/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#46 /var/www/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#47 /var/www/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#48 /var/www/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#49 /var/www/public/index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#50 {main}
"} 
[2025-06-19 09:29:40] local.ERROR: Method Illuminate\Http\Request::validated does not exist. {"userId":2,"exception":"[object] (BadMethodCallException(code: 0): Method Illuminate\\Http\\Request::validated does not exist. at /var/www/vendor/laravel/framework/src/Illuminate/Macroable/Traits/Macroable.php:115)
[stacktrace]
#0 /var/www/app/Http/Controllers/Api/V1/InvitationController.php(73): Illuminate\\Http\\Request->__call('validated', Array)
#1 /var/www/vendor/laravel/framework/src/Illuminate/Routing/Controller.php(54): App\\Http\\Controllers\\Api\\V1\\InvitationController->store(Object(Illuminate\\Http\\Request))
#2 /var/www/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('store', Array)
#3 /var/www/vendor/laravel/framework/src/Illuminate/Routing/Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Api\\V1\\InvitationController), 'store')
#4 /var/www/vendor/laravel/framework/src/Illuminate/Routing/Route.php(211): Illuminate\\Routing\\Route->runController()
#5 /var/www/vendor/laravel/framework/src/Illuminate/Routing/Router.php(808): Illuminate\\Routing\\Route->run()
#6 /var/www/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}(Object(Illuminate\\Http\\Request))
#7 /var/www/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authorize.php(59): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#8 /var/www/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authorize->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'create', 'App\\\\Models\\\\Invi...')
#9 /var/www/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#10 /var/www/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#11 /var/www/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#12 /var/www/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'sanctum')
#13 /var/www/vendor/laravel/sanctum/src/Http/Middleware/EnsureFrontendRequestsAreStateful.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#14 /var/www/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful->{closure:Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful::handle():25}(Object(Illuminate\\Http\\Request))
#15 /var/www/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#16 /var/www/vendor/laravel/sanctum/src/Http/Middleware/EnsureFrontendRequestsAreStateful.php(25): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#17 /var/www/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 /var/www/app/Http/Middleware/ApiMiddleware.php(29): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#19 /var/www/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): App\\Http\\Middleware\\ApiMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 /var/www/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#21 /var/www/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#22 /var/www/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#23 /var/www/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#24 /var/www/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#25 /var/www/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#26 /var/www/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#27 /var/www/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#28 /var/www/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 /var/www/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 /var/www/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#31 /var/www/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 /var/www/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 /var/www/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#34 /var/www/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 /var/www/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#36 /var/www/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 /var/www/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#38 /var/www/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 /var/www/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#40 /var/www/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 /var/www/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#42 /var/www/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 /var/www/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#44 /var/www/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 /var/www/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#46 /var/www/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#47 /var/www/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#48 /var/www/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#49 /var/www/public/index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#50 {main}
"} 
[2025-06-20 02:40:20] local.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 068947. This code will expire in 15 minutes."} 
[2025-06-20 02:40:20] local.INFO: Verification code sent {"email":"<EMAIL>","code":"068947","expires_at":"2025-06-20T02:55:20.343545Z"} 
[2025-06-20 03:30:20] local.WARNING: Verification code not found or expired {"email":"<EMAIL>"} 
[2025-06-20 03:30:38] local.WARNING: Verification code not found or expired {"email":"<EMAIL>"} 
[2025-06-20 03:30:59] local.WARNING: Verification code not found or expired {"email":"<EMAIL>"} 
[2025-06-20 03:31:28] local.WARNING: Verification code not found or expired {"email":"<EMAIL>"} 
[2025-06-20 03:32:11] local.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 768655. This code will expire in 15 minutes."} 
[2025-06-20 03:32:11] local.INFO: Verification code sent {"email":"<EMAIL>","code":"768655","expires_at":"2025-06-20T03:47:11.682324Z"} 
[2025-06-20 03:32:33] local.WARNING: Invalid verification code provided {"email":"<EMAIL>"} 
[2025-06-20 03:34:19] local.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 035764. This code will expire in 15 minutes."} 
[2025-06-20 03:34:19] local.INFO: Verification code sent {"email":"<EMAIL>","code":"035764","expires_at":"2025-06-20T03:49:19.467621Z"} 
[2025-06-20 03:35:36] local.INFO: Verification code verified successfully {"email":"<EMAIL>"} 
[2025-06-20 11:02:47] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 604224. This code will expire in 15 minutes."} 
[2025-06-20 11:02:47] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"604224","expires_at":"2025-06-20T11:17:47.489567Z"} 
[2025-06-20 11:02:47] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 003360. This code will expire in 15 minutes."} 
[2025-06-20 11:02:47] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"003360","expires_at":"2025-06-20T11:17:47.495960Z"} 
[2025-06-20 11:02:47] testing.INFO: Verification code verified successfully {"email":"<EMAIL>"} 
[2025-06-20 11:02:47] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 209262. This code will expire in 15 minutes."} 
[2025-06-20 11:02:47] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"209262","expires_at":"2025-06-20T11:17:47.501926Z"} 
[2025-06-20 11:02:47] testing.WARNING: Invalid verification code provided {"email":"<EMAIL>"} 
[2025-06-20 11:02:47] testing.WARNING: Verification code not found or expired {"email":"<EMAIL>"} 
[2025-06-20 11:02:47] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 594243. This code will expire in 15 minutes."} 
[2025-06-20 11:02:47] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"594243","expires_at":"2025-06-20T11:17:47.517656Z"} 
[2025-06-20 11:02:47] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 654736. This code will expire in 15 minutes."} 
[2025-06-20 11:02:47] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"654736","expires_at":"2025-06-20T11:17:47.517878Z"} 
[2025-06-20 11:02:47] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 876324. This code will expire in 15 minutes."} 
[2025-06-20 11:02:47] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"876324","expires_at":"2025-06-20T11:17:47.528711Z"} 
[2025-06-20 11:02:47] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 109707. This code will expire in 15 minutes."} 
[2025-06-20 11:02:47] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"109707","expires_at":"2025-06-20T11:17:47.534277Z"} 
[2025-06-20 11:02:47] testing.WARNING: Verification code resend blocked due to retry interval {"email":"<EMAIL>"} 
[2025-06-20 11:02:47] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 493893. This code will expire in 15 minutes."} 
[2025-06-20 11:02:47] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"493893","expires_at":"2025-06-20T11:17:47.540801Z"} 
[2025-06-20 11:02:47] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 050385. This code will expire in 15 minutes."} 
[2025-06-20 11:02:47] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"050385","expires_at":"2025-06-20T11:17:47.541015Z"} 
[2025-06-20 11:02:47] testing.WARNING: Invalid verification code provided {"email":"<EMAIL>"} 
[2025-06-20 11:02:47] testing.INFO: Verification code verified successfully {"email":"<EMAIL>"} 
[2025-06-20 11:02:47] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 936186. This code will expire in 15 minutes."} 
[2025-06-20 11:02:47] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"936186","expires_at":"2025-06-20T11:17:47.546341Z"} 
[2025-06-20 11:02:47] testing.INFO: Verification code verified successfully {"email":"<EMAIL>"} 
[2025-06-20 11:02:47] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 097978. This code will expire in 15 minutes."} 
[2025-06-20 11:02:47] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"097978","expires_at":"2025-06-20T11:17:47.551905Z"} 
[2025-06-20 11:02:47] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 942018. This code will expire in 15 minutes."} 
[2025-06-20 11:02:47] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"942018","expires_at":"2025-06-20T11:17:47.553553Z"} 
[2025-06-20 11:02:47] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 809880. This code will expire in 15 minutes."} 
[2025-06-20 11:02:47] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"809880","expires_at":"2025-06-20T11:17:47.553773Z"} 
[2025-06-20 11:02:47] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 897856. This code will expire in 15 minutes."} 
[2025-06-20 11:02:47] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"897856","expires_at":"2025-06-20T11:17:47.553975Z"} 
[2025-06-20 11:02:47] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 688649. This code will expire in 15 minutes."} 
[2025-06-20 11:02:47] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"688649","expires_at":"2025-06-20T11:17:47.554191Z"} 
[2025-06-20 11:02:47] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 580921. This code will expire in 15 minutes."} 
[2025-06-20 11:02:47] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"580921","expires_at":"2025-06-20T11:17:47.554385Z"} 
[2025-06-20 11:02:47] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 213113. This code will expire in 15 minutes."} 
[2025-06-20 11:02:47] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"213113","expires_at":"2025-06-20T11:17:47.554599Z"} 
[2025-06-20 11:02:47] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 567907. This code will expire in 15 minutes."} 
[2025-06-20 11:02:47] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"567907","expires_at":"2025-06-20T11:17:47.554811Z"} 
[2025-06-20 11:02:47] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 441503. This code will expire in 15 minutes."} 
[2025-06-20 11:02:47] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"441503","expires_at":"2025-06-20T11:17:47.555003Z"} 
[2025-06-20 11:02:47] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 865627. This code will expire in 15 minutes."} 
[2025-06-20 11:02:47] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"865627","expires_at":"2025-06-20T11:17:47.555207Z"} 
[2025-06-20 11:02:49] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 933573. This code will expire in 15 minutes."} 
[2025-06-20 11:02:49] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"933573","expires_at":"2025-06-20T11:17:49.248946Z"} 
[2025-06-20 11:02:49] testing.INFO: Verification code verified successfully {"email":"<EMAIL>"} 
[2025-06-20 11:02:49] testing.WARNING: Verification code not found or expired {"email":"<EMAIL>"} 
[2025-06-20 11:02:49] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 966616. This code will expire in 15 minutes."} 
[2025-06-20 11:02:49] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"966616","expires_at":"2025-06-20T11:17:49.283702Z"} 
[2025-06-20 11:02:49] testing.INFO: Verification code verified successfully {"email":"<EMAIL>"} 
[2025-06-20 11:02:49] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 347788. This code will expire in 15 minutes."} 
[2025-06-20 11:02:49] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"347788","expires_at":"2025-06-20T11:17:49.476722Z"} 
[2025-06-20 11:02:49] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 148825. This code will expire in 15 minutes."} 
[2025-06-20 11:02:49] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"148825","expires_at":"2025-06-20T11:17:49.506361Z"} 
[2025-06-20 12:17:57] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 467798. This code will expire in 15 minutes."} 
[2025-06-20 12:17:57] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"467798","expires_at":"2025-06-20T12:32:57.402332Z"} 
[2025-06-20 12:17:57] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 173611. This code will expire in 15 minutes."} 
[2025-06-20 12:17:57] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"173611","expires_at":"2025-06-20T12:32:57.408913Z"} 
[2025-06-20 12:17:57] testing.INFO: Verification code verified successfully {"email":"<EMAIL>"} 
[2025-06-20 12:17:57] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 372269. This code will expire in 15 minutes."} 
[2025-06-20 12:17:57] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"372269","expires_at":"2025-06-20T12:32:57.415028Z"} 
[2025-06-20 12:17:57] testing.WARNING: Invalid verification code provided {"email":"<EMAIL>"} 
[2025-06-20 12:17:57] testing.WARNING: Verification code not found or expired {"email":"<EMAIL>"} 
[2025-06-20 12:17:57] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 842211. This code will expire in 15 minutes."} 
[2025-06-20 12:17:57] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"842211","expires_at":"2025-06-20T12:32:57.430570Z"} 
[2025-06-20 12:17:57] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 492479. This code will expire in 15 minutes."} 
[2025-06-20 12:17:57] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"492479","expires_at":"2025-06-20T12:32:57.430797Z"} 
[2025-06-20 12:17:57] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 980807. This code will expire in 15 minutes."} 
[2025-06-20 12:17:57] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"980807","expires_at":"2025-06-20T12:32:57.441519Z"} 
[2025-06-20 12:17:57] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 553903. This code will expire in 15 minutes."} 
[2025-06-20 12:17:57] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"553903","expires_at":"2025-06-20T12:32:57.447168Z"} 
[2025-06-20 12:17:57] testing.WARNING: Verification code resend blocked due to retry interval {"email":"<EMAIL>"} 
[2025-06-20 12:17:57] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 044549. This code will expire in 15 minutes."} 
[2025-06-20 12:17:57] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"044549","expires_at":"2025-06-20T12:32:57.454047Z"} 
[2025-06-20 12:17:57] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 725455. This code will expire in 15 minutes."} 
[2025-06-20 12:17:57] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"725455","expires_at":"2025-06-20T12:32:57.454297Z"} 
[2025-06-20 12:17:57] testing.WARNING: Invalid verification code provided {"email":"<EMAIL>"} 
[2025-06-20 12:17:57] testing.INFO: Verification code verified successfully {"email":"<EMAIL>"} 
[2025-06-20 12:17:57] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 110979. This code will expire in 15 minutes."} 
[2025-06-20 12:17:57] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"110979","expires_at":"2025-06-20T12:32:57.459882Z"} 
[2025-06-20 12:17:57] testing.INFO: Verification code verified successfully {"email":"<EMAIL>"} 
[2025-06-20 12:17:57] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 396491. This code will expire in 15 minutes."} 
[2025-06-20 12:17:57] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"396491","expires_at":"2025-06-20T12:32:57.465415Z"} 
[2025-06-20 12:17:57] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 610733. This code will expire in 15 minutes."} 
[2025-06-20 12:17:57] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"610733","expires_at":"2025-06-20T12:32:57.467031Z"} 
[2025-06-20 12:17:57] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 986306. This code will expire in 15 minutes."} 
[2025-06-20 12:17:57] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"986306","expires_at":"2025-06-20T12:32:57.467268Z"} 
[2025-06-20 12:17:57] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 606409. This code will expire in 15 minutes."} 
[2025-06-20 12:17:57] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"606409","expires_at":"2025-06-20T12:32:57.467487Z"} 
[2025-06-20 12:17:57] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 898065. This code will expire in 15 minutes."} 
[2025-06-20 12:17:57] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"898065","expires_at":"2025-06-20T12:32:57.467691Z"} 
[2025-06-20 12:17:57] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 937669. This code will expire in 15 minutes."} 
[2025-06-20 12:17:57] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"937669","expires_at":"2025-06-20T12:32:57.467891Z"} 
[2025-06-20 12:17:57] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 093162. This code will expire in 15 minutes."} 
[2025-06-20 12:17:57] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"093162","expires_at":"2025-06-20T12:32:57.468088Z"} 
[2025-06-20 12:17:57] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 852863. This code will expire in 15 minutes."} 
[2025-06-20 12:17:57] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"852863","expires_at":"2025-06-20T12:32:57.468283Z"} 
[2025-06-20 12:17:57] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 446710. This code will expire in 15 minutes."} 
[2025-06-20 12:17:57] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"446710","expires_at":"2025-06-20T12:32:57.468487Z"} 
[2025-06-20 12:17:57] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 009016. This code will expire in 15 minutes."} 
[2025-06-20 12:17:57] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"009016","expires_at":"2025-06-20T12:32:57.468690Z"} 
[2025-06-20 12:17:59] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 625740. This code will expire in 15 minutes."} 
[2025-06-20 12:17:59] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"625740","expires_at":"2025-06-20T12:32:59.362664Z"} 
[2025-06-20 12:17:59] testing.INFO: Verification code verified successfully {"email":"<EMAIL>"} 
[2025-06-20 12:17:59] testing.WARNING: Verification code not found or expired {"email":"<EMAIL>"} 
[2025-06-20 12:17:59] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 621805. This code will expire in 15 minutes."} 
[2025-06-20 12:17:59] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"621805","expires_at":"2025-06-20T12:32:59.406300Z"} 
[2025-06-20 12:17:59] testing.INFO: Verification code verified successfully {"email":"<EMAIL>"} 
[2025-06-20 12:17:59] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 338343. This code will expire in 15 minutes."} 
[2025-06-20 12:17:59] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"338343","expires_at":"2025-06-20T12:32:59.606075Z"} 
[2025-06-20 12:17:59] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 921991. This code will expire in 15 minutes."} 
[2025-06-20 12:17:59] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"921991","expires_at":"2025-06-20T12:32:59.646725Z"} 
[2025-06-20 12:35:53] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 084801. This code will expire in 15 minutes."} 
[2025-06-20 12:35:53] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"084801","expires_at":"2025-06-20T12:50:53.986617Z"} 
[2025-06-20 12:35:53] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 370524. This code will expire in 15 minutes."} 
[2025-06-20 12:35:53] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"370524","expires_at":"2025-06-20T12:50:53.993210Z"} 
[2025-06-20 12:35:53] testing.INFO: Verification code verified successfully {"email":"<EMAIL>"} 
[2025-06-20 12:35:53] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 759197. This code will expire in 15 minutes."} 
[2025-06-20 12:35:53] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"759197","expires_at":"2025-06-20T12:50:53.999341Z"} 
[2025-06-20 12:35:53] testing.WARNING: Invalid verification code provided {"email":"<EMAIL>"} 
[2025-06-20 12:35:54] testing.WARNING: Verification code not found or expired {"email":"<EMAIL>"} 
[2025-06-20 12:35:54] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 109734. This code will expire in 15 minutes."} 
[2025-06-20 12:35:54] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"109734","expires_at":"2025-06-20T12:50:54.015241Z"} 
[2025-06-20 12:35:54] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 095277. This code will expire in 15 minutes."} 
[2025-06-20 12:35:54] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"095277","expires_at":"2025-06-20T12:50:54.015473Z"} 
[2025-06-20 12:35:54] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 068300. This code will expire in 15 minutes."} 
[2025-06-20 12:35:54] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"068300","expires_at":"2025-06-20T12:50:54.026378Z"} 
[2025-06-20 12:35:54] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 325662. This code will expire in 15 minutes."} 
[2025-06-20 12:35:54] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"325662","expires_at":"2025-06-20T12:50:54.031510Z"} 
[2025-06-20 12:35:54] testing.WARNING: Verification code resend blocked due to retry interval {"email":"<EMAIL>"} 
[2025-06-20 12:35:54] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 680757. This code will expire in 15 minutes."} 
[2025-06-20 12:35:54] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"680757","expires_at":"2025-06-20T12:50:54.038043Z"} 
[2025-06-20 12:35:54] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 562255. This code will expire in 15 minutes."} 
[2025-06-20 12:35:54] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"562255","expires_at":"2025-06-20T12:50:54.038279Z"} 
[2025-06-20 12:35:54] testing.WARNING: Invalid verification code provided {"email":"<EMAIL>"} 
[2025-06-20 12:35:54] testing.INFO: Verification code verified successfully {"email":"<EMAIL>"} 
[2025-06-20 12:35:54] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 206916. This code will expire in 15 minutes."} 
[2025-06-20 12:35:54] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"206916","expires_at":"2025-06-20T12:50:54.043882Z"} 
[2025-06-20 12:35:54] testing.INFO: Verification code verified successfully {"email":"<EMAIL>"} 
[2025-06-20 12:35:54] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 872381. This code will expire in 15 minutes."} 
[2025-06-20 12:35:54] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"872381","expires_at":"2025-06-20T12:50:54.049288Z"} 
[2025-06-20 12:35:54] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 751367. This code will expire in 15 minutes."} 
[2025-06-20 12:35:54] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"751367","expires_at":"2025-06-20T12:50:54.050846Z"} 
[2025-06-20 12:35:54] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 294470. This code will expire in 15 minutes."} 
[2025-06-20 12:35:54] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"294470","expires_at":"2025-06-20T12:50:54.051077Z"} 
[2025-06-20 12:35:54] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 141378. This code will expire in 15 minutes."} 
[2025-06-20 12:35:54] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"141378","expires_at":"2025-06-20T12:50:54.051281Z"} 
[2025-06-20 12:35:54] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 125336. This code will expire in 15 minutes."} 
[2025-06-20 12:35:54] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"125336","expires_at":"2025-06-20T12:50:54.051478Z"} 
[2025-06-20 12:35:54] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 352917. This code will expire in 15 minutes."} 
[2025-06-20 12:35:54] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"352917","expires_at":"2025-06-20T12:50:54.051685Z"} 
[2025-06-20 12:35:54] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 689280. This code will expire in 15 minutes."} 
[2025-06-20 12:35:54] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"689280","expires_at":"2025-06-20T12:50:54.051919Z"} 
[2025-06-20 12:35:54] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 796846. This code will expire in 15 minutes."} 
[2025-06-20 12:35:54] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"796846","expires_at":"2025-06-20T12:50:54.052115Z"} 
[2025-06-20 12:35:54] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 141548. This code will expire in 15 minutes."} 
[2025-06-20 12:35:54] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"141548","expires_at":"2025-06-20T12:50:54.052313Z"} 
[2025-06-20 12:35:54] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 896385. This code will expire in 15 minutes."} 
[2025-06-20 12:35:54] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"896385","expires_at":"2025-06-20T12:50:54.052518Z"} 
[2025-06-20 12:35:55] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 185668. This code will expire in 15 minutes."} 
[2025-06-20 12:35:55] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"185668","expires_at":"2025-06-20T12:50:55.909484Z"} 
[2025-06-20 12:35:55] testing.INFO: Verification code verified successfully {"email":"<EMAIL>"} 
[2025-06-20 12:35:55] testing.WARNING: Verification code not found or expired {"email":"<EMAIL>"} 
[2025-06-20 12:35:55] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 864592. This code will expire in 15 minutes."} 
[2025-06-20 12:35:55] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"864592","expires_at":"2025-06-20T12:50:55.960346Z"} 
[2025-06-20 12:35:55] testing.INFO: Verification code verified successfully {"email":"<EMAIL>"} 
[2025-06-20 12:35:56] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 038346. This code will expire in 15 minutes."} 
[2025-06-20 12:35:56] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"038346","expires_at":"2025-06-20T12:50:56.159125Z"} 
[2025-06-20 12:35:56] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 116661. This code will expire in 15 minutes."} 
[2025-06-20 12:35:56] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"116661","expires_at":"2025-06-20T12:50:56.196985Z"} 
