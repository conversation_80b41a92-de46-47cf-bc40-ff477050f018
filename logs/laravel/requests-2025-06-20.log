[2025-06-20 11:02:47] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/auth/login","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["53"],"content-type":["application/json"]},"body":{"email":"<EMAIL>","password":"***FILTERED***"},"timestamp":"2025-06-20T11:02:47.847301Z"} 
[2025-06-20 11:02:47] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/auth/login","status_code":200,"duration_ms":28.7,"response_size":274,"timestamp":"2025-06-20T11:02:47.875094Z"} 
[2025-06-20 11:02:47] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/auth/login","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["55"],"content-type":["application/json"]},"body":{"email":"<EMAIL>","password":"***FILTERED***"},"timestamp":"2025-06-20T11:02:47.886169Z"} 
[2025-06-20 11:02:47] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/auth/login","status_code":422,"duration_ms":3.08,"response_size":199,"timestamp":"2025-06-20T11:02:47.889180Z"} 
[2025-06-20 11:02:47] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/user","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"],"authorization":["***FILTERED***"]},"body":[],"timestamp":"2025-06-20T11:02:47.896972Z"} 
[2025-06-20 11:02:47] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/user","status_code":200,"duration_ms":8.45,"response_size":374,"timestamp":"2025-06-20T11:02:47.905353Z"} 
[2025-06-20 11:02:47] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/user","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:47.912380Z"} 
[2025-06-20 11:02:47] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/user","status_code":401,"duration_ms":0.61,"response_size":145,"timestamp":"2025-06-20T11:02:47.912938Z"} 
[2025-06-20 11:02:47] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/auth/login","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["53"],"content-type":["application/json"]},"body":{"email":"<EMAIL>","password":"***FILTERED***"},"timestamp":"2025-06-20T11:02:47.919605Z"} 
[2025-06-20 11:02:47] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/auth/login","status_code":200,"duration_ms":1.95,"response_size":274,"timestamp":"2025-06-20T11:02:47.921505Z"} 
[2025-06-20 11:02:47] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/user","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"],"authorization":["***FILTERED***"]},"body":[],"timestamp":"2025-06-20T11:02:47.922109Z"} 
[2025-06-20 11:02:47] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/user","status_code":200,"duration_ms":1.72,"response_size":374,"timestamp":"2025-06-20T11:02:47.923824Z"} 
[2025-06-20 11:02:47] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/auth/logout","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"],"authorization":["***FILTERED***"]},"body":[],"timestamp":"2025-06-20T11:02:47.924433Z"} 
[2025-06-20 11:02:47] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/auth/logout","status_code":200,"duration_ms":0.33,"response_size":117,"timestamp":"2025-06-20T11:02:47.924761Z"} 
[2025-06-20 11:02:47] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/user","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"],"authorization":["***FILTERED***"]},"body":[],"timestamp":"2025-06-20T11:02:47.931802Z"} 
[2025-06-20 11:02:47] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/user","status_code":200,"duration_ms":1.89,"response_size":374,"timestamp":"2025-06-20T11:02:47.933619Z"} 
[2025-06-20 11:02:47] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/auth/revoke-all-tokens","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"],"authorization":["***FILTERED***"]},"body":[],"timestamp":"2025-06-20T11:02:47.934266Z"} 
[2025-06-20 11:02:47] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/auth/revoke-all-tokens","status_code":200,"duration_ms":0.36,"response_size":123,"timestamp":"2025-06-20T11:02:47.934620Z"} 
[2025-06-20 11:02:47] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/auth/login","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["42"],"content-type":["application/json"]},"body":{"email":"invalid-email","password":"***FILTERED***"},"timestamp":"2025-06-20T11:02:47.941603Z"} 
[2025-06-20 11:02:47] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/auth/login","status_code":422,"duration_ms":3.18,"response_size":276,"timestamp":"2025-06-20T11:02:47.944727Z"} 
[2025-06-20 11:02:47] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/user","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:47.962385Z"} 
[2025-06-20 11:02:47] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/user","status_code":401,"duration_ms":0.63,"response_size":145,"timestamp":"2025-06-20T11:02:47.962934Z"} 
[2025-06-20 11:02:47] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:47.975963Z"} 
[2025-06-20 11:02:47] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/roles","status_code":403,"duration_ms":1.5,"response_size":142,"timestamp":"2025-06-20T11:02:47.977395Z"} 
[2025-06-20 11:02:47] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/auth/login","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["39"],"content-type":["application/json"]},"body":{"email":"invalid-email","password":null},"timestamp":"2025-06-20T11:02:47.990246Z"} 
[2025-06-20 11:02:47] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/auth/login","status_code":422,"duration_ms":0.96,"response_size":257,"timestamp":"2025-06-20T11:02:47.991139Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/user","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.003053Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/user","status_code":401,"duration_ms":0.42,"response_size":145,"timestamp":"2025-06-20T11:02:48.003412Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.017160Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations","status_code":401,"duration_ms":0.65,"response_size":145,"timestamp":"2025-06-20T11:02:48.017723Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.028819Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations","status_code":200,"duration_ms":9.9,"response_size":1606,"timestamp":"2025-06-20T11:02:48.038652Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.049620Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations","status_code":200,"duration_ms":1.87,"response_size":672,"timestamp":"2025-06-20T11:02:48.051423Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.060928Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations","status_code":403,"duration_ms":1.57,"response_size":134,"timestamp":"2025-06-20T11:02:48.062439Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations?role=member","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":{"role":"member"},"timestamp":"2025-06-20T11:02:48.072729Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations?role=member","status_code":200,"duration_ms":1.97,"response_size":1148,"timestamp":"2025-06-20T11:02:48.074638Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations?model_id=1","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":{"model_id":"1"},"timestamp":"2025-06-20T11:02:48.075196Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations?model_id=1","status_code":200,"duration_ms":1.64,"response_size":1146,"timestamp":"2025-06-20T11:02:48.076839Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations?model_type=App%5CModels%5COrganisation","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":{"model_type":"App\\Models\\Organisation"},"timestamp":"2025-06-20T11:02:48.077359Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations?model_type=App%5CModels%5COrganisation","status_code":200,"duration_ms":1.85,"response_size":1619,"timestamp":"2025-06-20T11:02:48.079209Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations?status=valid","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":{"status":"valid"},"timestamp":"2025-06-20T11:02:48.089929Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations?status=valid","status_code":200,"duration_ms":1.75,"response_size":670,"timestamp":"2025-06-20T11:02:48.091620Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations?status=expired","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":{"status":"expired"},"timestamp":"2025-06-20T11:02:48.092176Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations?status=expired","status_code":200,"duration_ms":1.45,"response_size":670,"timestamp":"2025-06-20T11:02:48.093620Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations?status=used_up","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":{"status":"used_up"},"timestamp":"2025-06-20T11:02:48.094182Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations?status=used_up","status_code":200,"duration_ms":1.5,"response_size":670,"timestamp":"2025-06-20T11:02:48.095682Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations?per_page=10","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":{"per_page":"10"},"timestamp":"2025-06-20T11:02:48.111082Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations?per_page=10","status_code":200,"duration_ms":3.05,"response_size":4855,"timestamp":"2025-06-20T11:02:48.114089Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations?page=2&per_page=10","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":{"per_page":"10","page":"2"},"timestamp":"2025-06-20T11:02:48.114723Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations?page=2&per_page=10","status_code":200,"duration_ms":2.75,"response_size":4856,"timestamp":"2025-06-20T11:02:48.117472Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations/01978d01-643e-70fb-849e-942a89438178","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.127616Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations/01978d01-643e-70fb-849e-942a89438178","status_code":200,"duration_ms":1.16,"response_size":589,"timestamp":"2025-06-20T11:02:48.128715Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations/01978d01-6449-7102-ae21-c289afea2838","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.138459Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations/01978d01-6449-7102-ae21-c289afea2838","status_code":200,"duration_ms":1.11,"response_size":595,"timestamp":"2025-06-20T11:02:48.139505Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations/01978d01-6455-71ed-9d08-fdae279a150c","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.150096Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations/01978d01-6455-71ed-9d08-fdae279a150c","status_code":410,"duration_ms":0.76,"response_size":147,"timestamp":"2025-06-20T11:02:48.150738Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations/01978d01-6460-7105-9882-6d2a081e2cc6","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.160787Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations/01978d01-6460-7105-9882-6d2a081e2cc6","status_code":410,"duration_ms":0.63,"response_size":171,"timestamp":"2025-06-20T11:02:48.161356Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations/01978d01-646a-7183-a634-32b2956d73ae","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.171156Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations/01978d01-646a-7183-a634-32b2956d73ae","status_code":200,"duration_ms":1.11,"response_size":618,"timestamp":"2025-06-20T11:02:48.172206Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations/01978d01-6475-73a8-8706-0f7e3fa40465","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.181901Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations/01978d01-6475-73a8-8706-0f7e3fa40465","status_code":200,"duration_ms":1.17,"response_size":589,"timestamp":"2025-06-20T11:02:48.183016Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/invitations/01978d01-6481-71df-b9b0-39778992617e/accept","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.193828Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/invitations/01978d01-6481-71df-b9b0-39778992617e/accept","status_code":422,"duration_ms":1.71,"response_size":223,"timestamp":"2025-06-20T11:02:48.195465Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/invitations/01978d01-648d-7086-82e1-257c97f195d0/accept","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.206396Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/invitations/01978d01-648d-7086-82e1-257c97f195d0/accept","status_code":401,"duration_ms":0.6,"response_size":145,"timestamp":"2025-06-20T11:02:48.206927Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/invitations/01978d01-6498-71c9-bef4-8a0282310e55/accept","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.217337Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/invitations/01978d01-6498-71c9-bef4-8a0282310e55/accept","status_code":200,"duration_ms":3.17,"response_size":462,"timestamp":"2025-06-20T11:02:48.220438Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/invitations/01978d01-64a7-7199-883b-41bfe9b518d5/accept","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.231868Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/invitations/01978d01-64a7-7199-883b-41bfe9b518d5/accept","status_code":422,"duration_ms":0.77,"response_size":246,"timestamp":"2025-06-20T11:02:48.232528Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/organisations","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.252597Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/organisations","status_code":200,"duration_ms":2.75,"response_size":1858,"timestamp":"2025-06-20T11:02:48.255255Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/organisations?status=active","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":{"status":"active"},"timestamp":"2025-06-20T11:02:48.266392Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/organisations?status=active","status_code":200,"duration_ms":2.01,"response_size":1122,"timestamp":"2025-06-20T11:02:48.268333Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/organisations","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["106"],"content-type":["application/json"]},"body":{"name":"Test Organisation","code":"TEST001","details":{"industry":"Technology"},"remarks":"Test remarks"},"timestamp":"2025-06-20T11:02:48.284042Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/organisations","status_code":201,"duration_ms":2.26,"response_size":390,"timestamp":"2025-06-20T11:02:48.286232Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/organisations","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.294660Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/organisations","status_code":422,"duration_ms":1.77,"response_size":252,"timestamp":"2025-06-20T11:02:48.296370Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/organisations","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["47"],"content-type":["application/json"]},"body":{"name":"Test Organisation","code":"DUPLICATE"},"timestamp":"2025-06-20T11:02:48.304987Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/organisations","status_code":422,"duration_ms":1.94,"response_size":186,"timestamp":"2025-06-20T11:02:48.306864Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/organisations/1","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.315464Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/organisations/1","status_code":200,"duration_ms":1.88,"response_size":476,"timestamp":"2025-06-20T11:02:48.317278Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/organisations/999","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.325498Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/organisations/999","status_code":404,"duration_ms":1.92,"response_size":135,"timestamp":"2025-06-20T11:02:48.327361Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"PUT","url":"http://localhost/api/v1/organisations/1","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["63"],"content-type":["application/json"]},"body":{"name":"New Name","code":"NEW001","remarks":"Updated remarks"},"timestamp":"2025-06-20T11:02:48.335924Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"PUT","url":"http://localhost/api/v1/organisations/1","status_code":200,"duration_ms":4.17,"response_size":416,"timestamp":"2025-06-20T11:02:48.340037Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"PUT","url":"http://localhost/api/v1/organisations/999","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["19"],"content-type":["application/json"]},"body":{"name":"New Name"},"timestamp":"2025-06-20T11:02:48.348803Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"PUT","url":"http://localhost/api/v1/organisations/999","status_code":404,"duration_ms":1.41,"response_size":135,"timestamp":"2025-06-20T11:02:48.350154Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/organisations/1/suspend","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.359456Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/organisations/1/suspend","status_code":200,"duration_ms":1.95,"response_size":443,"timestamp":"2025-06-20T11:02:48.361327Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/organisations/1/suspend","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.371241Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/organisations/1/suspend","status_code":422,"duration_ms":1.62,"response_size":186,"timestamp":"2025-06-20T11:02:48.372795Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/organisations","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.383250Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/organisations","status_code":401,"duration_ms":0.7,"response_size":145,"timestamp":"2025-06-20T11:02:48.383868Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/organisations","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.384504Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/organisations","status_code":401,"duration_ms":0.25,"response_size":145,"timestamp":"2025-06-20T11:02:48.384751Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/organisations/1","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.385279Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/organisations/1","status_code":401,"duration_ms":0.23,"response_size":145,"timestamp":"2025-06-20T11:02:48.385506Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"PUT","url":"http://localhost/api/v1/organisations/1","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.386159Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"PUT","url":"http://localhost/api/v1/organisations/1","status_code":401,"duration_ms":0.36,"response_size":145,"timestamp":"2025-06-20T11:02:48.386479Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/organisations/1/suspend","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.387181Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/organisations/1/suspend","status_code":401,"duration_ms":0.27,"response_size":145,"timestamp":"2025-06-20T11:02:48.387438Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/organisations/1","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.398057Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/organisations/1","status_code":200,"duration_ms":2.04,"response_size":495,"timestamp":"2025-06-20T11:02:48.400014Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/organisations/2","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.410641Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/organisations/2","status_code":403,"duration_ms":1.75,"response_size":134,"timestamp":"2025-06-20T11:02:48.412330Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/organisations/1","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.422393Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/organisations/1","status_code":403,"duration_ms":1.74,"response_size":134,"timestamp":"2025-06-20T11:02:48.424077Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.443347Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/roles","status_code":200,"duration_ms":1.55,"response_size":417,"timestamp":"2025-06-20T11:02:48.444820Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.457602Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/roles","status_code":200,"duration_ms":1.49,"response_size":417,"timestamp":"2025-06-20T11:02:48.459029Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.477940Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/roles","status_code":200,"duration_ms":1.58,"response_size":407,"timestamp":"2025-06-20T11:02:48.479431Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.492279Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/roles","status_code":200,"duration_ms":1.54,"response_size":407,"timestamp":"2025-06-20T11:02:48.493757Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.506587Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/roles","status_code":403,"duration_ms":0.82,"response_size":142,"timestamp":"2025-06-20T11:02:48.507327Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.520006Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/roles","status_code":403,"duration_ms":0.78,"response_size":142,"timestamp":"2025-06-20T11:02:48.520716Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.533040Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/roles","status_code":401,"duration_ms":0.52,"response_size":145,"timestamp":"2025-06-20T11:02:48.533505Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.545859Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/roles","status_code":403,"duration_ms":0.75,"response_size":142,"timestamp":"2025-06-20T11:02:48.546550Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.559246Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/roles","status_code":200,"duration_ms":1.45,"response_size":417,"timestamp":"2025-06-20T11:02:48.560637Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.573253Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/roles","status_code":200,"duration_ms":1.48,"response_size":417,"timestamp":"2025-06-20T11:02:48.574675Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.587349Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/roles","status_code":403,"duration_ms":0.77,"response_size":142,"timestamp":"2025-06-20T11:02:48.588054Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.600555Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/roles","status_code":200,"duration_ms":1.46,"response_size":407,"timestamp":"2025-06-20T11:02:48.601956Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["59"],"content-type":["application/json"]},"body":{"name":"test-role","organisation_id":1,"guard_name":"api"},"timestamp":"2025-06-20T11:02:48.614749Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/roles","status_code":201,"duration_ms":2.13,"response_size":256,"timestamp":"2025-06-20T11:02:48.616823Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["49"],"content-type":["application/json"]},"body":{"name":"test-system-role","guard_name":"system"},"timestamp":"2025-06-20T11:02:48.629712Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/roles","status_code":201,"duration_ms":1.85,"response_size":269,"timestamp":"2025-06-20T11:02:48.631498Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["65"],"content-type":["application/json"]},"body":{"name":"invalid-role","organisation_id":1,"guard_name":"system"},"timestamp":"2025-06-20T11:02:48.644175Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/roles","status_code":422,"duration_ms":1.63,"response_size":217,"timestamp":"2025-06-20T11:02:48.645745Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/roles/3","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.658217Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/roles/3","status_code":200,"duration_ms":1.22,"response_size":254,"timestamp":"2025-06-20T11:02:48.659384Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.671683Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/roles","status_code":401,"duration_ms":0.52,"response_size":145,"timestamp":"2025-06-20T11:02:48.672150Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.684432Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/roles","status_code":200,"duration_ms":1.42,"response_size":417,"timestamp":"2025-06-20T11:02:48.685797Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.698720Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/roles","status_code":403,"duration_ms":0.8,"response_size":142,"timestamp":"2025-06-20T11:02:48.699449Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/users","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.727285Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/users","status_code":200,"duration_ms":2.77,"response_size":2204,"timestamp":"2025-06-20T11:02:48.729974Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/users","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.743690Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/users","status_code":200,"duration_ms":2.48,"response_size":2192,"timestamp":"2025-06-20T11:02:48.746108Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/users","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.759370Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/users","status_code":200,"duration_ms":2.38,"response_size":800,"timestamp":"2025-06-20T11:02:48.761686Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/users?organisation_id=1","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":{"organisation_id":"1"},"timestamp":"2025-06-20T11:02:48.774956Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/users?organisation_id=1","status_code":200,"duration_ms":2.29,"response_size":787,"timestamp":"2025-06-20T11:02:48.777176Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/users?organisation_id=2","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":{"organisation_id":"2"},"timestamp":"2025-06-20T11:02:48.790560Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/users?organisation_id=2","status_code":422,"duration_ms":1.37,"response_size":197,"timestamp":"2025-06-20T11:02:48.791870Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/users","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.805197Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/users","status_code":200,"duration_ms":2.32,"response_size":798,"timestamp":"2025-06-20T11:02:48.807461Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/users","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.821421Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/users","status_code":403,"duration_ms":0.97,"response_size":134,"timestamp":"2025-06-20T11:02:48.822328Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/users/6","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.835607Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/users/6","status_code":200,"duration_ms":1.36,"response_size":411,"timestamp":"2025-06-20T11:02:48.836900Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/users/6","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.850339Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/users/6","status_code":200,"duration_ms":1.54,"response_size":422,"timestamp":"2025-06-20T11:02:48.851824Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/users/7","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.864958Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/users/7","status_code":403,"duration_ms":1.18,"response_size":134,"timestamp":"2025-06-20T11:02:48.866088Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/users/4","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.879131Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/users/4","status_code":200,"duration_ms":1.61,"response_size":410,"timestamp":"2025-06-20T11:02:48.880685Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/users/6","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.893817Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/users/6","status_code":403,"duration_ms":1.18,"response_size":134,"timestamp":"2025-06-20T11:02:48.894938Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["99"],"content-type":["application/json"]},"body":{"name":"New User","email":"<EMAIL>","password":"***FILTERED***","organisation_ids":[1,2]},"timestamp":"2025-06-20T11:02:48.908205Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users","status_code":201,"duration_ms":3.3,"response_size":438,"timestamp":"2025-06-20T11:02:48.911444Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["104"],"content-type":["application/json"]},"body":{"name":"New Org User","email":"<EMAIL>","password":"***FILTERED***","organisation_ids":[1]},"timestamp":"2025-06-20T11:02:48.925089Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users","status_code":201,"duration_ms":3.31,"response_size":375,"timestamp":"2025-06-20T11:02:48.928336Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["101"],"content-type":["application/json"]},"body":{"name":"Invalid User","email":"<EMAIL>","password":"***FILTERED***","organisation_ids":[2]},"timestamp":"2025-06-20T11:02:48.941866Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users","status_code":422,"duration_ms":2.01,"response_size":215,"timestamp":"2025-06-20T11:02:48.943821Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["85"],"content-type":["application/json"]},"body":{"name":"Default Org User","email":"<EMAIL>","password":"***FILTERED***"},"timestamp":"2025-06-20T11:02:48.957248Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users","status_code":201,"duration_ms":3.24,"response_size":381,"timestamp":"2025-06-20T11:02:48.960421Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["88"],"content-type":["application/json"]},"body":{"name":"Unauthorized User","email":"<EMAIL>","password":"***FILTERED***"},"timestamp":"2025-06-20T11:02:48.973921Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users","status_code":403,"duration_ms":1.01,"response_size":134,"timestamp":"2025-06-20T11:02:48.974875Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"PUT","url":"http://localhost/api/v1/users/6","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["23"],"content-type":["application/json"]},"body":{"name":"Updated Name"},"timestamp":"2025-06-20T11:02:48.996379Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"PUT","url":"http://localhost/api/v1/users/6","status_code":200,"duration_ms":2.1,"response_size":399,"timestamp":"2025-06-20T11:02:48.998412Z"} 
[2025-06-20 11:02:49] testing.INFO: API Request {"method":"PUT","url":"http://localhost/api/v1/users/6","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["30"],"content-type":["application/json"]},"body":{"name":"Updated Member Name"},"timestamp":"2025-06-20T11:02:49.012324Z"} 
[2025-06-20 11:02:49] testing.INFO: API Response {"method":"PUT","url":"http://localhost/api/v1/users/6","status_code":200,"duration_ms":2.27,"response_size":404,"timestamp":"2025-06-20T11:02:49.014509Z"} 
[2025-06-20 11:02:49] testing.INFO: API Request {"method":"PUT","url":"http://localhost/api/v1/users/7","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["28"],"content-type":["application/json"]},"body":{"name":"Should Not Update"},"timestamp":"2025-06-20T11:02:49.027943Z"} 
[2025-06-20 11:02:49] testing.INFO: API Response {"method":"PUT","url":"http://localhost/api/v1/users/7","status_code":403,"duration_ms":1.42,"response_size":134,"timestamp":"2025-06-20T11:02:49.029300Z"} 
[2025-06-20 11:02:49] testing.INFO: API Request {"method":"PUT","url":"http://localhost/api/v1/users/6","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["30"],"content-type":["application/json"]},"body":{"name":"Unauthorized Update"},"timestamp":"2025-06-20T11:02:49.042747Z"} 
[2025-06-20 11:02:49] testing.INFO: API Response {"method":"PUT","url":"http://localhost/api/v1/users/6","status_code":403,"duration_ms":1.19,"response_size":134,"timestamp":"2025-06-20T11:02:49.043868Z"} 
[2025-06-20 11:02:49] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/6/suspend","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:49.057868Z"} 
[2025-06-20 11:02:49] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/6/suspend","status_code":200,"duration_ms":1.57,"response_size":404,"timestamp":"2025-06-20T11:02:49.059380Z"} 
[2025-06-20 11:02:49] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/6/suspend","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:49.073469Z"} 
[2025-06-20 11:02:49] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/6/suspend","status_code":200,"duration_ms":1.9,"response_size":399,"timestamp":"2025-06-20T11:02:49.075310Z"} 
[2025-06-20 11:02:49] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/7/suspend","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:49.089180Z"} 
[2025-06-20 11:02:49] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/7/suspend","status_code":403,"duration_ms":1.3,"response_size":134,"timestamp":"2025-06-20T11:02:49.090400Z"} 
[2025-06-20 11:02:49] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/6/activate","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:49.104566Z"} 
[2025-06-20 11:02:49] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/6/activate","status_code":200,"duration_ms":1.5,"response_size":400,"timestamp":"2025-06-20T11:02:49.105992Z"} 
[2025-06-20 11:02:49] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/6/activate","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:49.119792Z"} 
[2025-06-20 11:02:49] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/6/activate","status_code":200,"duration_ms":1.7,"response_size":405,"timestamp":"2025-06-20T11:02:49.121435Z"} 
[2025-06-20 11:02:49] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/7/activate","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:49.135194Z"} 
[2025-06-20 11:02:49] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/7/activate","status_code":403,"duration_ms":1.23,"response_size":134,"timestamp":"2025-06-20T11:02:49.136365Z"} 
[2025-06-20 11:02:49] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/6/suspend","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:49.150684Z"} 
[2025-06-20 11:02:49] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/6/suspend","status_code":403,"duration_ms":1.31,"response_size":134,"timestamp":"2025-06-20T11:02:49.151922Z"} 
[2025-06-20 11:02:49] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/6/activate","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:49.165886Z"} 
[2025-06-20 11:02:49] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/6/activate","status_code":403,"duration_ms":1.26,"response_size":134,"timestamp":"2025-06-20T11:02:49.167081Z"} 
[2025-06-20 11:02:49] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/users","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:49.187902Z"} 
[2025-06-20 11:02:49] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/users","status_code":200,"duration_ms":2.89,"response_size":1228,"timestamp":"2025-06-20T11:02:49.190707Z"} 
[2025-06-20 11:02:49] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/users?organisation_id=1","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":{"organisation_id":"1"},"timestamp":"2025-06-20T11:02:49.201849Z"} 
[2025-06-20 11:02:49] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/users?organisation_id=1","status_code":200,"duration_ms":2.98,"response_size":856,"timestamp":"2025-06-20T11:02:49.204753Z"} 
[2025-06-20 11:02:49] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["99"],"content-type":["application/json"]},"body":{"name":"New User","email":"<EMAIL>","password":"***FILTERED***","organisation_ids":[1,2]},"timestamp":"2025-06-20T11:02:49.223076Z"} 
[2025-06-20 11:02:49] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users","status_code":201,"duration_ms":3.86,"response_size":446,"timestamp":"2025-06-20T11:02:49.226865Z"} 
[2025-06-20 11:02:49] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["76"],"content-type":["application/json"]},"body":{"name":"Simple User","email":"<EMAIL>","password":"***FILTERED***"},"timestamp":"2025-06-20T11:02:49.236590Z"} 
[2025-06-20 11:02:49] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users","status_code":201,"duration_ms":3.3,"response_size":303,"timestamp":"2025-06-20T11:02:49.239815Z"} 
[2025-06-20 11:02:49] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/register","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["103"],"content-type":["application/json"]},"body":{"name":"Guest User","email":"<EMAIL>","password":"***FILTERED***","verification_code":"933573"},"timestamp":"2025-06-20T11:02:49.249573Z"} 
[2025-06-20 11:02:49] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/register","status_code":201,"duration_ms":2.45,"response_size":293,"timestamp":"2025-06-20T11:02:49.251967Z"} 
[2025-06-20 11:02:49] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/register","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["103"],"content-type":["application/json"]},"body":{"name":"Guest User","email":"<EMAIL>","password":"***FILTERED***","verification_code":"123456"},"timestamp":"2025-06-20T11:02:49.261880Z"} 
[2025-06-20 11:02:49] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/register","status_code":422,"duration_ms":1.3,"response_size":194,"timestamp":"2025-06-20T11:02:49.263111Z"} 
[2025-06-20 11:02:49] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/register","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["74"],"content-type":["application/json"]},"body":{"name":"Guest User","email":"<EMAIL>","password":"***FILTERED***"},"timestamp":"2025-06-20T11:02:49.272966Z"} 
[2025-06-20 11:02:49] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/register","status_code":422,"duration_ms":1.32,"response_size":186,"timestamp":"2025-06-20T11:02:49.274210Z"} 
[2025-06-20 11:02:49] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/register","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["126"],"content-type":["application/json"]},"body":{"name":"Guest User","email":"<EMAIL>","password":"***FILTERED***","verification_code":"966616","organisation_ids":[1]},"timestamp":"2025-06-20T11:02:49.284347Z"} 
[2025-06-20 11:02:49] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/register","status_code":422,"duration_ms":1.12,"response_size":218,"timestamp":"2025-06-20T11:02:49.285411Z"} 
[2025-06-20 11:02:49] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:49.295461Z"} 
[2025-06-20 11:02:49] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users","status_code":422,"duration_ms":1.91,"response_size":305,"timestamp":"2025-06-20T11:02:49.297300Z"} 
[2025-06-20 11:02:49] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["77"],"content-type":["application/json"]},"body":{"name":"Test User","email":"<EMAIL>","password":"***FILTERED***"},"timestamp":"2025-06-20T11:02:49.306751Z"} 
[2025-06-20 11:02:49] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users","status_code":422,"duration_ms":2.0,"response_size":199,"timestamp":"2025-06-20T11:02:49.308694Z"} 
[2025-06-20 11:02:49] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["102"],"content-type":["application/json"]},"body":{"name":"Test User","email":"<EMAIL>","password":"***FILTERED***","organisation_ids":[999,1000]},"timestamp":"2025-06-20T11:02:49.317636Z"} 
[2025-06-20 11:02:49] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users","status_code":422,"duration_ms":2.38,"response_size":280,"timestamp":"2025-06-20T11:02:49.319957Z"} 
[2025-06-20 11:02:49] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/users/3","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:49.329429Z"} 
[2025-06-20 11:02:49] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/users/3","status_code":200,"duration_ms":2.01,"response_size":481,"timestamp":"2025-06-20T11:02:49.331362Z"} 
[2025-06-20 11:02:49] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/users/999","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:49.340939Z"} 
[2025-06-20 11:02:49] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/users/999","status_code":404,"duration_ms":1.34,"response_size":135,"timestamp":"2025-06-20T11:02:49.342210Z"} 
[2025-06-20 11:02:49] testing.INFO: API Request {"method":"PUT","url":"http://localhost/api/v1/users/3","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["42"],"content-type":["application/json"]},"body":{"name":"New Name","organisation_ids":[2]},"timestamp":"2025-06-20T11:02:49.351927Z"} 
[2025-06-20 11:02:49] testing.INFO: API Response {"method":"PUT","url":"http://localhost/api/v1/users/3","status_code":200,"duration_ms":3.93,"response_size":394,"timestamp":"2025-06-20T11:02:49.355795Z"} 
[2025-06-20 11:02:49] testing.INFO: API Request {"method":"PUT","url":"http://localhost/api/v1/users/999","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["19"],"content-type":["application/json"]},"body":{"name":"New Name"},"timestamp":"2025-06-20T11:02:49.365563Z"} 
[2025-06-20 11:02:49] testing.INFO: API Response {"method":"PUT","url":"http://localhost/api/v1/users/999","status_code":404,"duration_ms":1.27,"response_size":135,"timestamp":"2025-06-20T11:02:49.366770Z"} 
[2025-06-20 11:02:49] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/3/organisations/1","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:49.376099Z"} 
[2025-06-20 11:02:49] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/3/organisations/1","status_code":200,"duration_ms":2.31,"response_size":423,"timestamp":"2025-06-20T11:02:49.378351Z"} 
[2025-06-20 11:02:49] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/3/organisations/1","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:49.389122Z"} 
[2025-06-20 11:02:49] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/3/organisations/1","status_code":200,"duration_ms":3.37,"response_size":422,"timestamp":"2025-06-20T11:02:49.392406Z"} 
[2025-06-20 11:02:49] testing.INFO: API Request {"method":"DELETE","url":"http://localhost/api/v1/users/3/organisations/1","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:49.402643Z"} 
[2025-06-20 11:02:49] testing.INFO: API Response {"method":"DELETE","url":"http://localhost/api/v1/users/3/organisations/1","status_code":200,"duration_ms":2.83,"response_size":436,"timestamp":"2025-06-20T11:02:49.405411Z"} 
[2025-06-20 11:02:49] testing.INFO: API Request {"method":"PUT","url":"http://localhost/api/v1/users/3/organisations","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["24"],"content-type":["application/json"]},"body":{"organisation_ids":[2]},"timestamp":"2025-06-20T11:02:49.415222Z"} 
[2025-06-20 11:02:49] testing.INFO: API Response {"method":"PUT","url":"http://localhost/api/v1/users/3/organisations","status_code":200,"duration_ms":3.21,"response_size":427,"timestamp":"2025-06-20T11:02:49.418372Z"} 
[2025-06-20 11:02:49] testing.INFO: API Request {"method":"PUT","url":"http://localhost/api/v1/users/3/organisations","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["23"],"content-type":["application/json"]},"body":{"organisation_ids":[]},"timestamp":"2025-06-20T11:02:49.428105Z"} 
[2025-06-20 11:02:49] testing.INFO: API Response {"method":"PUT","url":"http://localhost/api/v1/users/3/organisations","status_code":200,"duration_ms":3.75,"response_size":359,"timestamp":"2025-06-20T11:02:49.431791Z"} 
[2025-06-20 11:02:49] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/3/suspend","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:49.449309Z"} 
[2025-06-20 11:02:49] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/3/suspend","status_code":200,"duration_ms":2.02,"response_size":351,"timestamp":"2025-06-20T11:02:49.451256Z"} 
[2025-06-20 11:02:49] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/users","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:49.460710Z"} 
[2025-06-20 11:02:49] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/users","status_code":401,"duration_ms":0.53,"response_size":145,"timestamp":"2025-06-20T11:02:49.461179Z"} 
[2025-06-20 11:02:49] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:49.461794Z"} 
[2025-06-20 11:02:49] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users","status_code":401,"duration_ms":0.3,"response_size":145,"timestamp":"2025-06-20T11:02:49.462088Z"} 
[2025-06-20 11:02:49] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/users/3","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:49.462644Z"} 
[2025-06-20 11:02:49] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/users/3","status_code":401,"duration_ms":0.23,"response_size":145,"timestamp":"2025-06-20T11:02:49.462866Z"} 
[2025-06-20 11:02:49] testing.INFO: API Request {"method":"PUT","url":"http://localhost/api/v1/users/3","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:49.463398Z"} 
[2025-06-20 11:02:49] testing.INFO: API Response {"method":"PUT","url":"http://localhost/api/v1/users/3","status_code":401,"duration_ms":0.22,"response_size":145,"timestamp":"2025-06-20T11:02:49.463617Z"} 
[2025-06-20 11:02:49] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/3/suspend","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:49.464129Z"} 
[2025-06-20 11:02:49] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/3/suspend","status_code":401,"duration_ms":0.22,"response_size":145,"timestamp":"2025-06-20T11:02:49.464347Z"} 
[2025-06-20 11:02:49] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/3/organisations/1","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:49.464882Z"} 
[2025-06-20 11:02:49] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/3/organisations/1","status_code":401,"duration_ms":0.22,"response_size":145,"timestamp":"2025-06-20T11:02:49.465100Z"} 
[2025-06-20 11:02:49] testing.INFO: API Request {"method":"DELETE","url":"http://localhost/api/v1/users/3/organisations/1","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:49.465607Z"} 
[2025-06-20 11:02:49] testing.INFO: API Response {"method":"DELETE","url":"http://localhost/api/v1/users/3/organisations/1","status_code":401,"duration_ms":0.23,"response_size":145,"timestamp":"2025-06-20T11:02:49.465834Z"} 
[2025-06-20 11:02:49] testing.INFO: API Request {"method":"PUT","url":"http://localhost/api/v1/users/3/organisations","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:49.466399Z"} 
[2025-06-20 11:02:49] testing.INFO: API Response {"method":"PUT","url":"http://localhost/api/v1/users/3/organisations","status_code":401,"duration_ms":0.25,"response_size":145,"timestamp":"2025-06-20T11:02:49.466644Z"} 
[2025-06-20 11:02:49] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/send-verification-code","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["28"],"content-type":["application/json"]},"body":{"email":"<EMAIL>"},"timestamp":"2025-06-20T11:02:49.475961Z"} 
[2025-06-20 11:02:49] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/send-verification-code","status_code":200,"duration_ms":1.15,"response_size":197,"timestamp":"2025-06-20T11:02:49.477040Z"} 
[2025-06-20 11:02:49] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/send-verification-code","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["25"],"content-type":["application/json"]},"body":{"email":"invalid-email"},"timestamp":"2025-06-20T11:02:49.486370Z"} 
[2025-06-20 11:02:49] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/send-verification-code","status_code":422,"duration_ms":0.91,"response_size":176,"timestamp":"2025-06-20T11:02:49.487217Z"} 
[2025-06-20 11:02:49] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/send-verification-code","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["32"],"content-type":["application/json"]},"body":{"email":"<EMAIL>"},"timestamp":"2025-06-20T11:02:49.496614Z"} 
[2025-06-20 11:02:49] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/send-verification-code","status_code":422,"duration_ms":1.05,"response_size":181,"timestamp":"2025-06-20T11:02:49.497602Z"} 
[2025-06-20 11:02:49] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/send-verification-code","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["28"],"content-type":["application/json"]},"body":{"email":"<EMAIL>"},"timestamp":"2025-06-20T11:02:49.506994Z"} 
[2025-06-20 11:02:49] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/send-verification-code","status_code":422,"duration_ms":0.72,"response_size":185,"timestamp":"2025-06-20T11:02:49.507664Z"} 
[2025-06-20 11:02:49] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/6/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["13"],"content-type":["application/json"]},"body":{"role_id":4},"timestamp":"2025-06-20T11:02:49.530977Z"} 
[2025-06-20 11:02:49] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/6/roles","status_code":200,"duration_ms":2.34,"response_size":180,"timestamp":"2025-06-20T11:02:49.533233Z"} 
[2025-06-20 11:02:49] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/6/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["13"],"content-type":["application/json"]},"body":{"role_id":4},"timestamp":"2025-06-20T11:02:49.546156Z"} 
[2025-06-20 11:02:49] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/6/roles","status_code":200,"duration_ms":2.24,"response_size":180,"timestamp":"2025-06-20T11:02:49.548319Z"} 
[2025-06-20 11:02:49] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/6/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["13"],"content-type":["application/json"]},"body":{"role_id":4},"timestamp":"2025-06-20T11:02:49.560221Z"} 
[2025-06-20 11:02:49] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/6/roles","status_code":200,"duration_ms":2.77,"response_size":180,"timestamp":"2025-06-20T11:02:49.562950Z"} 
[2025-06-20 11:02:49] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/6/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["13"],"content-type":["application/json"]},"body":{"role_id":3},"timestamp":"2025-06-20T11:02:49.574932Z"} 
[2025-06-20 11:02:49] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/6/roles","status_code":422,"duration_ms":2.1,"response_size":295,"timestamp":"2025-06-20T11:02:49.576963Z"} 
[2025-06-20 11:02:49] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/6/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["13"],"content-type":["application/json"]},"body":{"role_id":4},"timestamp":"2025-06-20T11:02:49.589342Z"} 
[2025-06-20 11:02:49] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/6/roles","status_code":403,"duration_ms":2.27,"response_size":134,"timestamp":"2025-06-20T11:02:49.591547Z"} 
[2025-06-20 11:02:49] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/6/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["13"],"content-type":["application/json"]},"body":{"role_id":3},"timestamp":"2025-06-20T11:02:49.603506Z"} 
[2025-06-20 11:02:49] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/6/roles","status_code":422,"duration_ms":2.1,"response_size":295,"timestamp":"2025-06-20T11:02:49.605542Z"} 
[2025-06-20 11:02:49] testing.INFO: API Request {"method":"PUT","url":"http://localhost/api/v1/users/6/transfer-owner","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["21"],"content-type":["application/json"]},"body":{"organisation_id":1},"timestamp":"2025-06-20T11:02:49.617255Z"} 
[2025-06-20 11:02:49] testing.INFO: API Response {"method":"PUT","url":"http://localhost/api/v1/users/6/transfer-owner","status_code":200,"duration_ms":3.33,"response_size":193,"timestamp":"2025-06-20T11:02:49.620527Z"} 
[2025-06-20 11:02:49] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/user","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:49.632525Z"} 
[2025-06-20 11:02:49] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/user","status_code":200,"duration_ms":1.35,"response_size":560,"timestamp":"2025-06-20T11:02:49.633818Z"} 
[2025-06-20 11:02:49] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/users/assignable-roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:49.644855Z"} 
[2025-06-20 11:02:49] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/users/assignable-roles","status_code":200,"duration_ms":1.61,"response_size":164,"timestamp":"2025-06-20T11:02:49.646410Z"} 
[2025-06-20 11:02:49] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/health","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:49.656542Z"} 
[2025-06-20 11:02:49] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/health","status_code":200,"duration_ms":11.6,"response_size":263,"timestamp":"2025-06-20T11:02:49.668082Z"} 
[2025-06-20 11:02:49] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/status","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:49.681118Z"} 
[2025-06-20 11:02:49] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/status","status_code":200,"duration_ms":0.54,"response_size":390,"timestamp":"2025-06-20T11:02:49.681582Z"} 
[2025-06-20 11:02:49] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/health","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:49.687281Z"} 
[2025-06-20 11:02:49] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/health","status_code":200,"duration_ms":1.03,"response_size":263,"timestamp":"2025-06-20T11:02:49.688269Z"} 
[2025-06-20 11:02:49] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/status","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:49.699644Z"} 
[2025-06-20 11:02:49] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/status","status_code":200,"duration_ms":0.49,"response_size":390,"timestamp":"2025-06-20T11:02:49.700072Z"} 
[2025-06-20 11:02:49] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/status","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:49.705739Z"} 
[2025-06-20 11:02:49] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/status","status_code":200,"duration_ms":0.5,"response_size":390,"timestamp":"2025-06-20T11:02:49.706181Z"} 
[2025-06-20 12:02:57] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/status","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:02:57.028245Z"} 
[2025-06-20 12:02:57] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/status","status_code":200,"duration_ms":20.67,"response_size":377,"timestamp":"2025-06-20T12:02:57.041395Z"} 
[2025-06-20 12:02:57] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/status","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["zh-CN,zh;q=0.9,en;q=0.8"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:02:57.068792Z"} 
[2025-06-20 12:02:57] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/status","status_code":200,"duration_ms":0.68,"response_size":378,"timestamp":"2025-06-20T12:02:57.069385Z"} 
[2025-06-20 12:02:57] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/status","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"],"x-locale":["zh"]},"body":[],"timestamp":"2025-06-20T12:02:57.075130Z"} 
[2025-06-20 12:02:57] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/status","status_code":200,"duration_ms":0.52,"response_size":377,"timestamp":"2025-06-20T12:02:57.075598Z"} 
[2025-06-20 12:02:57] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/status","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"],"x-locale":["en"]},"body":[],"timestamp":"2025-06-20T12:02:57.093166Z"} 
[2025-06-20 12:02:57] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/status","status_code":200,"duration_ms":0.57,"response_size":377,"timestamp":"2025-06-20T12:02:57.093681Z"} 
[2025-06-20 12:02:57] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/status","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"],"x-locale":["fr"]},"body":[],"timestamp":"2025-06-20T12:02:57.099143Z"} 
[2025-06-20 12:02:57] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/status","status_code":200,"duration_ms":0.58,"response_size":377,"timestamp":"2025-06-20T12:02:57.099670Z"} 
[2025-06-20 12:02:57] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/status","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["zh-CN,zh;q=0.9,en;q=0.8"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"],"x-locale":["en"]},"body":[],"timestamp":"2025-06-20T12:02:57.105220Z"} 
[2025-06-20 12:02:57] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/status","status_code":200,"duration_ms":0.57,"response_size":378,"timestamp":"2025-06-20T12:02:57.105739Z"} 
[2025-06-20 12:04:59] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/status","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:04:59.645892Z"} 
[2025-06-20 12:04:59] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/status","status_code":200,"duration_ms":9.04,"response_size":387,"timestamp":"2025-06-20T12:04:59.651837Z"} 
[2025-06-20 12:04:59] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/status","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["zh-CN,zh;q=0.9,en;q=0.8"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:04:59.666382Z"} 
[2025-06-20 12:04:59] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/status","status_code":200,"duration_ms":0.64,"response_size":388,"timestamp":"2025-06-20T12:04:59.666947Z"} 
[2025-06-20 12:04:59] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/status","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"],"x-locale":["zh"]},"body":[],"timestamp":"2025-06-20T12:04:59.673337Z"} 
[2025-06-20 12:04:59] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/status","status_code":200,"duration_ms":0.59,"response_size":388,"timestamp":"2025-06-20T12:04:59.673870Z"} 
[2025-06-20 12:04:59] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/status","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"],"x-locale":["en"]},"body":[],"timestamp":"2025-06-20T12:04:59.679692Z"} 
[2025-06-20 12:04:59] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/status","status_code":200,"duration_ms":0.56,"response_size":387,"timestamp":"2025-06-20T12:04:59.680195Z"} 
[2025-06-20 12:04:59] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/status","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"],"x-locale":["fr"]},"body":[],"timestamp":"2025-06-20T12:04:59.686201Z"} 
[2025-06-20 12:04:59] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/status","status_code":200,"duration_ms":0.57,"response_size":387,"timestamp":"2025-06-20T12:04:59.686711Z"} 
[2025-06-20 12:04:59] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/status","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["zh-CN,zh;q=0.9,en;q=0.8"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"],"x-locale":["en"]},"body":[],"timestamp":"2025-06-20T12:04:59.692801Z"} 
[2025-06-20 12:04:59] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/status","status_code":200,"duration_ms":0.57,"response_size":387,"timestamp":"2025-06-20T12:04:59.693312Z"} 
[2025-06-20 12:13:42] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/auth/login","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["53"],"content-type":["application/json"]},"body":{"email":"<EMAIL>","password":"***FILTERED***"},"timestamp":"2025-06-20T12:13:42.045207Z"} 
[2025-06-20 12:13:42] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/auth/login","status_code":200,"duration_ms":30.22,"response_size":266,"timestamp":"2025-06-20T12:13:42.072113Z"} 
[2025-06-20 12:13:42] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/auth/login","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["55"],"content-type":["application/json"]},"body":{"email":"<EMAIL>","password":"***FILTERED***"},"timestamp":"2025-06-20T12:13:42.086414Z"} 
[2025-06-20 12:13:42] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/auth/login","status_code":422,"duration_ms":4.06,"response_size":184,"timestamp":"2025-06-20T12:13:42.090417Z"} 
[2025-06-20 12:13:42] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/user","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"],"authorization":["***FILTERED***"]},"body":[],"timestamp":"2025-06-20T12:13:42.099427Z"} 
[2025-06-20 12:13:42] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/user","status_code":200,"duration_ms":11.34,"response_size":374,"timestamp":"2025-06-20T12:13:42.110663Z"} 
[2025-06-20 12:13:42] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/user","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:13:42.118727Z"} 
[2025-06-20 12:13:42] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/user","status_code":401,"duration_ms":0.75,"response_size":145,"timestamp":"2025-06-20T12:13:42.119409Z"} 
[2025-06-20 12:13:42] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/auth/login","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["53"],"content-type":["application/json"]},"body":{"email":"<EMAIL>","password":"***FILTERED***"},"timestamp":"2025-06-20T12:13:42.128791Z"} 
[2025-06-20 12:13:42] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/auth/login","status_code":200,"duration_ms":2.23,"response_size":266,"timestamp":"2025-06-20T12:13:42.130959Z"} 
[2025-06-20 12:13:42] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/user","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"],"authorization":["***FILTERED***"]},"body":[],"timestamp":"2025-06-20T12:13:42.131654Z"} 
[2025-06-20 12:13:42] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/user","status_code":200,"duration_ms":1.8,"response_size":374,"timestamp":"2025-06-20T12:13:42.133444Z"} 
[2025-06-20 12:13:42] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/auth/logout","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"],"authorization":["***FILTERED***"]},"body":[],"timestamp":"2025-06-20T12:13:42.134189Z"} 
[2025-06-20 12:13:42] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/auth/logout","status_code":200,"duration_ms":0.35,"response_size":98,"timestamp":"2025-06-20T12:13:42.134530Z"} 
[2025-06-20 12:13:42] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/user","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"],"authorization":["***FILTERED***"]},"body":[],"timestamp":"2025-06-20T12:13:42.142741Z"} 
[2025-06-20 12:13:42] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/user","status_code":200,"duration_ms":2.03,"response_size":374,"timestamp":"2025-06-20T12:13:42.144708Z"} 
[2025-06-20 12:13:42] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/auth/revoke-all-tokens","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"],"authorization":["***FILTERED***"]},"body":[],"timestamp":"2025-06-20T12:13:42.145359Z"} 
[2025-06-20 12:13:42] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/auth/revoke-all-tokens","status_code":200,"duration_ms":0.43,"response_size":99,"timestamp":"2025-06-20T12:13:42.145784Z"} 
[2025-06-20 12:13:42] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/auth/login","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["42"],"content-type":["application/json"]},"body":{"email":"invalid-email","password":"***FILTERED***"},"timestamp":"2025-06-20T12:13:42.153879Z"} 
[2025-06-20 12:13:42] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/auth/login","status_code":422,"duration_ms":3.76,"response_size":276,"timestamp":"2025-06-20T12:13:42.157533Z"} 
[2025-06-20 12:14:12] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/user","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"],"x-locale":["en"]},"body":[],"timestamp":"2025-06-20T12:14:12.648404Z"} 
[2025-06-20 12:14:12] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/user","status_code":401,"duration_ms":7.27,"response_size":145,"timestamp":"2025-06-20T12:14:12.652186Z"} 
[2025-06-20 12:14:12] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/user","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"],"x-locale":["zh"]},"body":[],"timestamp":"2025-06-20T12:14:12.659894Z"} 
[2025-06-20 12:14:12] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/user","status_code":401,"duration_ms":0.77,"response_size":158,"timestamp":"2025-06-20T12:14:12.660591Z"} 
[2025-06-20 12:14:12] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/auth/login","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"],"x-locale":["en"]},"body":[],"timestamp":"2025-06-20T12:14:12.669228Z"} 
[2025-06-20 12:14:12] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/auth/login","status_code":422,"duration_ms":7.03,"response_size":245,"timestamp":"2025-06-20T12:14:12.676188Z"} 
[2025-06-20 12:14:12] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/auth/login","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"],"x-locale":["zh"]},"body":[],"timestamp":"2025-06-20T12:14:12.683305Z"} 
[2025-06-20 12:14:12] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/auth/login","status_code":422,"duration_ms":1.03,"response_size":252,"timestamp":"2025-06-20T12:14:12.684266Z"} 
[2025-06-20 12:14:12] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/auth/login","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["55"],"content-type":["application/json"],"x-locale":["en"]},"body":{"email":"<EMAIL>","password":"***FILTERED***"},"timestamp":"2025-06-20T12:14:12.691079Z"} 
[2025-06-20 12:14:12] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/auth/login","status_code":422,"duration_ms":4.74,"response_size":184,"timestamp":"2025-06-20T12:14:12.695760Z"} 
[2025-06-20 12:14:12] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/auth/login","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["55"],"content-type":["application/json"],"x-locale":["zh"]},"body":{"email":"<EMAIL>","password":"***FILTERED***"},"timestamp":"2025-06-20T12:14:12.706489Z"} 
[2025-06-20 12:14:12] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/auth/login","status_code":422,"duration_ms":1.54,"response_size":206,"timestamp":"2025-06-20T12:14:12.707939Z"} 
[2025-06-20 12:14:49] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/user","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"],"x-locale":["en"]},"body":[],"timestamp":"2025-06-20T12:14:49.212857Z"} 
[2025-06-20 12:14:49] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/user","status_code":401,"duration_ms":7.21,"response_size":145,"timestamp":"2025-06-20T12:14:49.216719Z"} 
[2025-06-20 12:14:49] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/user","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"],"x-locale":["zh"]},"body":[],"timestamp":"2025-06-20T12:14:49.224229Z"} 
[2025-06-20 12:14:49] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/user","status_code":401,"duration_ms":0.72,"response_size":158,"timestamp":"2025-06-20T12:14:49.224885Z"} 
[2025-06-20 12:14:49] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/auth/login","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"],"x-locale":["en"]},"body":[],"timestamp":"2025-06-20T12:14:49.232885Z"} 
[2025-06-20 12:14:49] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/auth/login","status_code":422,"duration_ms":6.71,"response_size":245,"timestamp":"2025-06-20T12:14:49.239505Z"} 
[2025-06-20 12:14:49] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/auth/login","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"],"x-locale":["zh"]},"body":[],"timestamp":"2025-06-20T12:14:49.246282Z"} 
[2025-06-20 12:14:49] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/auth/login","status_code":422,"duration_ms":0.93,"response_size":252,"timestamp":"2025-06-20T12:14:49.247144Z"} 
[2025-06-20 12:14:49] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/auth/login","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["55"],"content-type":["application/json"],"x-locale":["en"]},"body":{"email":"<EMAIL>","password":"***FILTERED***"},"timestamp":"2025-06-20T12:14:49.253533Z"} 
[2025-06-20 12:14:49] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/auth/login","status_code":422,"duration_ms":4.87,"response_size":184,"timestamp":"2025-06-20T12:14:49.258348Z"} 
[2025-06-20 12:14:49] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/auth/login","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["55"],"content-type":["application/json"],"x-locale":["zh"]},"body":{"email":"<EMAIL>","password":"***FILTERED***"},"timestamp":"2025-06-20T12:14:49.265875Z"} 
[2025-06-20 12:14:49] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/auth/login","status_code":422,"duration_ms":1.29,"response_size":206,"timestamp":"2025-06-20T12:14:49.267093Z"} 
[2025-06-20 12:17:41] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/user","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"],"x-locale":["en"]},"body":[],"timestamp":"2025-06-20T12:17:41.676099Z"} 
[2025-06-20 12:17:41] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/user","status_code":401,"duration_ms":6.56,"response_size":145,"timestamp":"2025-06-20T12:17:41.679405Z"} 
[2025-06-20 12:17:41] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/user","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"],"x-locale":["zh"]},"body":[],"timestamp":"2025-06-20T12:17:41.686180Z"} 
[2025-06-20 12:17:41] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/user","status_code":401,"duration_ms":0.69,"response_size":158,"timestamp":"2025-06-20T12:17:41.686820Z"} 
[2025-06-20 12:17:41] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/auth/login","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"],"x-locale":["en"]},"body":[],"timestamp":"2025-06-20T12:17:41.694559Z"} 
[2025-06-20 12:17:41] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/auth/login","status_code":422,"duration_ms":6.21,"response_size":245,"timestamp":"2025-06-20T12:17:41.700704Z"} 
[2025-06-20 12:17:41] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/auth/login","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"],"x-locale":["zh"]},"body":[],"timestamp":"2025-06-20T12:17:41.707020Z"} 
[2025-06-20 12:17:41] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/auth/login","status_code":422,"duration_ms":0.94,"response_size":252,"timestamp":"2025-06-20T12:17:41.707896Z"} 
[2025-06-20 12:17:41] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/auth/login","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["55"],"content-type":["application/json"],"x-locale":["en"]},"body":{"email":"<EMAIL>","password":"***FILTERED***"},"timestamp":"2025-06-20T12:17:41.713982Z"} 
[2025-06-20 12:17:41] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/auth/login","status_code":422,"duration_ms":4.31,"response_size":184,"timestamp":"2025-06-20T12:17:41.718233Z"} 
[2025-06-20 12:17:41] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/auth/login","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["55"],"content-type":["application/json"],"x-locale":["zh"]},"body":{"email":"<EMAIL>","password":"***FILTERED***"},"timestamp":"2025-06-20T12:17:41.725214Z"} 
[2025-06-20 12:17:41] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/auth/login","status_code":422,"duration_ms":1.28,"response_size":206,"timestamp":"2025-06-20T12:17:41.726427Z"} 
[2025-06-20 12:17:57] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/auth/login","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["53"],"content-type":["application/json"]},"body":{"email":"<EMAIL>","password":"***FILTERED***"},"timestamp":"2025-06-20T12:17:57.770983Z"} 
[2025-06-20 12:17:57] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/auth/login","status_code":200,"duration_ms":10.64,"response_size":266,"timestamp":"2025-06-20T12:17:57.781227Z"} 
[2025-06-20 12:17:57] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/auth/login","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["55"],"content-type":["application/json"]},"body":{"email":"<EMAIL>","password":"***FILTERED***"},"timestamp":"2025-06-20T12:17:57.791048Z"} 
[2025-06-20 12:17:57] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/auth/login","status_code":422,"duration_ms":2.77,"response_size":184,"timestamp":"2025-06-20T12:17:57.793758Z"} 
[2025-06-20 12:17:57] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/user","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"],"authorization":["***FILTERED***"]},"body":[],"timestamp":"2025-06-20T12:17:57.801660Z"} 
[2025-06-20 12:17:57] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/user","status_code":200,"duration_ms":4.41,"response_size":374,"timestamp":"2025-06-20T12:17:57.806034Z"} 
[2025-06-20 12:17:57] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/user","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:17:57.813592Z"} 
[2025-06-20 12:17:57] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/user","status_code":401,"duration_ms":0.77,"response_size":145,"timestamp":"2025-06-20T12:17:57.814299Z"} 
[2025-06-20 12:17:57] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/auth/login","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["53"],"content-type":["application/json"]},"body":{"email":"<EMAIL>","password":"***FILTERED***"},"timestamp":"2025-06-20T12:17:57.821584Z"} 
[2025-06-20 12:17:57] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/auth/login","status_code":200,"duration_ms":2.14,"response_size":266,"timestamp":"2025-06-20T12:17:57.823665Z"} 
[2025-06-20 12:17:57] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/user","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"],"authorization":["***FILTERED***"]},"body":[],"timestamp":"2025-06-20T12:17:57.824290Z"} 
[2025-06-20 12:17:57] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/user","status_code":200,"duration_ms":1.87,"response_size":374,"timestamp":"2025-06-20T12:17:57.826157Z"} 
[2025-06-20 12:17:57] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/auth/logout","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"],"authorization":["***FILTERED***"]},"body":[],"timestamp":"2025-06-20T12:17:57.826843Z"} 
[2025-06-20 12:17:57] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/auth/logout","status_code":200,"duration_ms":0.36,"response_size":98,"timestamp":"2025-06-20T12:17:57.827200Z"} 
[2025-06-20 12:17:57] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/user","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"],"authorization":["***FILTERED***"]},"body":[],"timestamp":"2025-06-20T12:17:57.834706Z"} 
[2025-06-20 12:17:57] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/user","status_code":200,"duration_ms":1.94,"response_size":374,"timestamp":"2025-06-20T12:17:57.836592Z"} 
[2025-06-20 12:17:57] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/auth/revoke-all-tokens","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"],"authorization":["***FILTERED***"]},"body":[],"timestamp":"2025-06-20T12:17:57.837238Z"} 
[2025-06-20 12:17:57] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/auth/revoke-all-tokens","status_code":200,"duration_ms":0.45,"response_size":99,"timestamp":"2025-06-20T12:17:57.837682Z"} 
[2025-06-20 12:17:57] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/auth/login","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["42"],"content-type":["application/json"]},"body":{"email":"invalid-email","password":"***FILTERED***"},"timestamp":"2025-06-20T12:17:57.845371Z"} 
[2025-06-20 12:17:57] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/auth/login","status_code":422,"duration_ms":1.74,"response_size":276,"timestamp":"2025-06-20T12:17:57.847040Z"} 
[2025-06-20 12:17:57] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/user","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:17:57.864678Z"} 
[2025-06-20 12:17:57] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/user","status_code":401,"duration_ms":0.7,"response_size":145,"timestamp":"2025-06-20T12:17:57.865302Z"} 
[2025-06-20 12:17:57] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:17:57.878711Z"} 
[2025-06-20 12:17:57] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/roles","status_code":403,"duration_ms":10.5,"response_size":142,"timestamp":"2025-06-20T12:17:57.889151Z"} 
[2025-06-20 12:17:57] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/auth/login","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["39"],"content-type":["application/json"]},"body":{"email":"invalid-email","password":null},"timestamp":"2025-06-20T12:17:57.902785Z"} 
[2025-06-20 12:17:57] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/auth/login","status_code":422,"duration_ms":1.07,"response_size":257,"timestamp":"2025-06-20T12:17:57.903791Z"} 
[2025-06-20 12:17:57] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/user","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:17:57.916960Z"} 
[2025-06-20 12:17:57] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/user","status_code":401,"duration_ms":0.53,"response_size":145,"timestamp":"2025-06-20T12:17:57.917418Z"} 
[2025-06-20 12:17:57] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:17:57.932236Z"} 
[2025-06-20 12:17:57] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations","status_code":401,"duration_ms":0.71,"response_size":145,"timestamp":"2025-06-20T12:17:57.932872Z"} 
[2025-06-20 12:17:57] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:17:57.950269Z"} 
[2025-06-20 12:17:57] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations","status_code":200,"duration_ms":4.42,"response_size":1605,"timestamp":"2025-06-20T12:17:57.954607Z"} 
[2025-06-20 12:17:57] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:17:57.966100Z"} 
[2025-06-20 12:17:57] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations","status_code":200,"duration_ms":2.07,"response_size":672,"timestamp":"2025-06-20T12:17:57.968084Z"} 
[2025-06-20 12:17:57] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:17:57.978206Z"} 
[2025-06-20 12:17:57] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations","status_code":403,"duration_ms":1.68,"response_size":134,"timestamp":"2025-06-20T12:17:57.979822Z"} 
[2025-06-20 12:17:57] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations?role=member","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":{"role":"member"},"timestamp":"2025-06-20T12:17:57.991403Z"} 
[2025-06-20 12:17:57] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations?role=member","status_code":200,"duration_ms":2.16,"response_size":1156,"timestamp":"2025-06-20T12:17:57.993491Z"} 
[2025-06-20 12:17:57] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations?model_id=1","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":{"model_id":"1"},"timestamp":"2025-06-20T12:17:57.994178Z"} 
[2025-06-20 12:17:57] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations?model_id=1","status_code":200,"duration_ms":1.7,"response_size":1154,"timestamp":"2025-06-20T12:17:57.995876Z"} 
[2025-06-20 12:17:57] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations?model_type=App%5CModels%5COrganisation","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":{"model_type":"App\\Models\\Organisation"},"timestamp":"2025-06-20T12:17:57.996468Z"} 
[2025-06-20 12:17:57] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations?model_type=App%5CModels%5COrganisation","status_code":200,"duration_ms":1.93,"response_size":1631,"timestamp":"2025-06-20T12:17:57.998403Z"} 
[2025-06-20 12:17:58] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations?status=valid","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":{"status":"valid"},"timestamp":"2025-06-20T12:17:58.010049Z"} 
[2025-06-20 12:17:58] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations?status=valid","status_code":200,"duration_ms":2.08,"response_size":667,"timestamp":"2025-06-20T12:17:58.012049Z"} 
[2025-06-20 12:17:58] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations?status=expired","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":{"status":"expired"},"timestamp":"2025-06-20T12:17:58.012860Z"} 
[2025-06-20 12:17:58] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations?status=expired","status_code":200,"duration_ms":2.01,"response_size":667,"timestamp":"2025-06-20T12:17:58.014868Z"} 
[2025-06-20 12:17:58] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations?status=used_up","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":{"status":"used_up"},"timestamp":"2025-06-20T12:17:58.015691Z"} 
[2025-06-20 12:17:58] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations?status=used_up","status_code":200,"duration_ms":1.77,"response_size":667,"timestamp":"2025-06-20T12:17:58.017449Z"} 
[2025-06-20 12:17:58] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations?per_page=10","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":{"per_page":"10"},"timestamp":"2025-06-20T12:17:58.034047Z"} 
[2025-06-20 12:17:58] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations?per_page=10","status_code":200,"duration_ms":3.32,"response_size":4855,"timestamp":"2025-06-20T12:17:58.037288Z"} 
[2025-06-20 12:17:58] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations?page=2&per_page=10","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":{"per_page":"10","page":"2"},"timestamp":"2025-06-20T12:17:58.038066Z"} 
[2025-06-20 12:17:58] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations?page=2&per_page=10","status_code":200,"duration_ms":2.94,"response_size":4856,"timestamp":"2025-06-20T12:17:58.041007Z"} 
[2025-06-20 12:17:58] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations/01978d46-3523-71ed-8a6d-0d0ca6e01aba","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:17:58.051843Z"} 
[2025-06-20 12:17:58] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations/01978d46-3523-71ed-8a6d-0d0ca6e01aba","status_code":200,"duration_ms":1.28,"response_size":593,"timestamp":"2025-06-20T12:17:58.053055Z"} 
[2025-06-20 12:17:58] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations/01978d46-352f-7025-bc24-007bf43f8db0","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:17:58.064547Z"} 
[2025-06-20 12:17:58] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations/01978d46-352f-7025-bc24-007bf43f8db0","status_code":200,"duration_ms":1.33,"response_size":593,"timestamp":"2025-06-20T12:17:58.065809Z"} 
[2025-06-20 12:17:58] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations/01978d46-353c-7357-92cf-2e5f3cb252c4","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:17:58.077009Z"} 
[2025-06-20 12:17:58] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations/01978d46-353c-7357-92cf-2e5f3cb252c4","status_code":410,"duration_ms":0.75,"response_size":147,"timestamp":"2025-06-20T12:17:58.077694Z"} 
[2025-06-20 12:17:58] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations/01978d46-3547-7272-ae89-899352f68b53","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:17:58.088203Z"} 
[2025-06-20 12:17:58] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations/01978d46-3547-7272-ae89-899352f68b53","status_code":410,"duration_ms":0.74,"response_size":171,"timestamp":"2025-06-20T12:17:58.088882Z"} 
[2025-06-20 12:17:58] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations/01978d46-3552-7376-afaf-07942534dd98","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:17:58.099590Z"} 
[2025-06-20 12:17:58] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations/01978d46-3552-7376-afaf-07942534dd98","status_code":200,"duration_ms":1.3,"response_size":605,"timestamp":"2025-06-20T12:17:58.100827Z"} 
[2025-06-20 12:17:58] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations/01978d46-355e-7127-a581-ff9e5ac97974","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:17:58.111684Z"} 
[2025-06-20 12:17:58] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations/01978d46-355e-7127-a581-ff9e5ac97974","status_code":200,"duration_ms":1.27,"response_size":593,"timestamp":"2025-06-20T12:17:58.112884Z"} 
[2025-06-20 12:17:58] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/invitations/01978d46-356b-73df-8300-d4855430b538/accept","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:17:58.124060Z"} 
[2025-06-20 12:17:58] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/invitations/01978d46-356b-73df-8300-d4855430b538/accept","status_code":422,"duration_ms":1.81,"response_size":223,"timestamp":"2025-06-20T12:17:58.125804Z"} 
[2025-06-20 12:17:58] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/invitations/01978d46-3577-7192-9ec9-91d2e79b5329/accept","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:17:58.136151Z"} 
[2025-06-20 12:17:58] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/invitations/01978d46-3577-7192-9ec9-91d2e79b5329/accept","status_code":401,"duration_ms":0.66,"response_size":145,"timestamp":"2025-06-20T12:17:58.136743Z"} 
[2025-06-20 12:17:58] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/invitations/01978d46-3582-732a-a11f-4856eaf0f386/accept","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:17:58.146831Z"} 
[2025-06-20 12:17:58] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/invitations/01978d46-3582-732a-a11f-4856eaf0f386/accept","status_code":200,"duration_ms":3.13,"response_size":462,"timestamp":"2025-06-20T12:17:58.149902Z"} 
[2025-06-20 12:17:58] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/invitations/01978d46-3590-73fb-a229-175ce757cfc9/accept","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:17:58.160773Z"} 
[2025-06-20 12:17:58] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/invitations/01978d46-3590-73fb-a229-175ce757cfc9/accept","status_code":422,"duration_ms":0.78,"response_size":246,"timestamp":"2025-06-20T12:17:58.161488Z"} 
[2025-06-20 12:17:58] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/status","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:17:58.174841Z"} 
[2025-06-20 12:17:58] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/status","status_code":200,"duration_ms":0.65,"response_size":391,"timestamp":"2025-06-20T12:17:58.175421Z"} 
[2025-06-20 12:17:58] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/status","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["zh-CN,zh;q=0.9,en;q=0.8"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:17:58.181872Z"} 
[2025-06-20 12:17:58] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/status","status_code":200,"duration_ms":0.6,"response_size":392,"timestamp":"2025-06-20T12:17:58.182404Z"} 
[2025-06-20 12:17:58] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/status","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"],"x-locale":["zh"]},"body":[],"timestamp":"2025-06-20T12:17:58.188543Z"} 
[2025-06-20 12:17:58] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/status","status_code":200,"duration_ms":0.71,"response_size":392,"timestamp":"2025-06-20T12:17:58.189181Z"} 
[2025-06-20 12:17:58] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/status","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"],"x-locale":["en"]},"body":[],"timestamp":"2025-06-20T12:17:58.195479Z"} 
[2025-06-20 12:17:58] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/status","status_code":200,"duration_ms":0.6,"response_size":391,"timestamp":"2025-06-20T12:17:58.196008Z"} 
[2025-06-20 12:17:58] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/status","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"],"x-locale":["fr"]},"body":[],"timestamp":"2025-06-20T12:17:58.210079Z"} 
[2025-06-20 12:17:58] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/status","status_code":200,"duration_ms":0.72,"response_size":391,"timestamp":"2025-06-20T12:17:58.210683Z"} 
[2025-06-20 12:17:58] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/status","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["zh-CN,zh;q=0.9,en;q=0.8"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"],"x-locale":["en"]},"body":[],"timestamp":"2025-06-20T12:17:58.216718Z"} 
[2025-06-20 12:17:58] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/status","status_code":200,"duration_ms":0.52,"response_size":391,"timestamp":"2025-06-20T12:17:58.217190Z"} 
[2025-06-20 12:17:58] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/user","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"],"x-locale":["en"]},"body":[],"timestamp":"2025-06-20T12:17:58.240615Z"} 
[2025-06-20 12:17:58] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/user","status_code":401,"duration_ms":0.68,"response_size":145,"timestamp":"2025-06-20T12:17:58.241226Z"} 
[2025-06-20 12:17:58] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/user","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"],"x-locale":["zh"]},"body":[],"timestamp":"2025-06-20T12:17:58.247296Z"} 
[2025-06-20 12:17:58] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/user","status_code":401,"duration_ms":0.65,"response_size":158,"timestamp":"2025-06-20T12:17:58.247887Z"} 
[2025-06-20 12:17:58] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/auth/login","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"],"x-locale":["en"]},"body":[],"timestamp":"2025-06-20T12:17:58.253904Z"} 
[2025-06-20 12:17:58] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/auth/login","status_code":422,"duration_ms":0.97,"response_size":245,"timestamp":"2025-06-20T12:17:58.254820Z"} 
[2025-06-20 12:17:58] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/auth/login","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"],"x-locale":["zh"]},"body":[],"timestamp":"2025-06-20T12:17:58.260661Z"} 
[2025-06-20 12:17:58] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/auth/login","status_code":422,"duration_ms":0.9,"response_size":252,"timestamp":"2025-06-20T12:17:58.261508Z"} 
[2025-06-20 12:17:58] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/auth/login","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["55"],"content-type":["application/json"],"x-locale":["en"]},"body":{"email":"<EMAIL>","password":"***FILTERED***"},"timestamp":"2025-06-20T12:17:58.267496Z"} 
[2025-06-20 12:17:58] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/auth/login","status_code":422,"duration_ms":1.3,"response_size":184,"timestamp":"2025-06-20T12:17:58.268742Z"} 
[2025-06-20 12:17:58] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/auth/login","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["55"],"content-type":["application/json"],"x-locale":["zh"]},"body":{"email":"<EMAIL>","password":"***FILTERED***"},"timestamp":"2025-06-20T12:17:58.274682Z"} 
[2025-06-20 12:17:58] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/auth/login","status_code":422,"duration_ms":1.1,"response_size":206,"timestamp":"2025-06-20T12:17:58.275723Z"} 
[2025-06-20 12:17:58] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/organisations","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:17:58.291280Z"} 
[2025-06-20 12:17:58] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/organisations","status_code":200,"duration_ms":2.67,"response_size":1759,"timestamp":"2025-06-20T12:17:58.293862Z"} 
[2025-06-20 12:17:58] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/organisations?status=active","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":{"status":"active"},"timestamp":"2025-06-20T12:17:58.309234Z"} 
[2025-06-20 12:17:58] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/organisations?status=active","status_code":200,"duration_ms":2.19,"response_size":1113,"timestamp":"2025-06-20T12:17:58.311352Z"} 
[2025-06-20 12:17:58] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/organisations","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["106"],"content-type":["application/json"]},"body":{"name":"Test Organisation","code":"TEST001","details":{"industry":"Technology"},"remarks":"Test remarks"},"timestamp":"2025-06-20T12:17:58.320241Z"} 
[2025-06-20 12:17:58] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/organisations","status_code":201,"duration_ms":2.43,"response_size":387,"timestamp":"2025-06-20T12:17:58.322604Z"} 
[2025-06-20 12:17:58] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/organisations","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:17:58.331704Z"} 
[2025-06-20 12:17:58] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/organisations","status_code":422,"duration_ms":1.93,"response_size":252,"timestamp":"2025-06-20T12:17:58.333567Z"} 
[2025-06-20 12:17:58] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/organisations","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["47"],"content-type":["application/json"]},"body":{"name":"Test Organisation","code":"DUPLICATE"},"timestamp":"2025-06-20T12:17:58.342826Z"} 
[2025-06-20 12:17:58] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/organisations","status_code":422,"duration_ms":2.09,"response_size":186,"timestamp":"2025-06-20T12:17:58.344852Z"} 
[2025-06-20 12:17:58] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/organisations/1","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:17:58.354290Z"} 
[2025-06-20 12:17:58] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/organisations/1","status_code":200,"duration_ms":2.04,"response_size":477,"timestamp":"2025-06-20T12:17:58.356257Z"} 
[2025-06-20 12:17:58] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/organisations/999","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:17:58.365751Z"} 
[2025-06-20 12:17:58] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/organisations/999","status_code":404,"duration_ms":2.4,"response_size":135,"timestamp":"2025-06-20T12:17:58.368082Z"} 
[2025-06-20 12:17:58] testing.INFO: API Request {"method":"PUT","url":"http://localhost/api/v1/organisations/1","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["63"],"content-type":["application/json"]},"body":{"name":"New Name","code":"NEW001","remarks":"Updated remarks"},"timestamp":"2025-06-20T12:17:58.378200Z"} 
[2025-06-20 12:17:58] testing.INFO: API Response {"method":"PUT","url":"http://localhost/api/v1/organisations/1","status_code":200,"duration_ms":4.36,"response_size":411,"timestamp":"2025-06-20T12:17:58.382491Z"} 
[2025-06-20 12:17:58] testing.INFO: API Request {"method":"PUT","url":"http://localhost/api/v1/organisations/999","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["19"],"content-type":["application/json"]},"body":{"name":"New Name"},"timestamp":"2025-06-20T12:17:58.398404Z"} 
[2025-06-20 12:17:58] testing.INFO: API Response {"method":"PUT","url":"http://localhost/api/v1/organisations/999","status_code":404,"duration_ms":1.4,"response_size":135,"timestamp":"2025-06-20T12:17:58.399736Z"} 
[2025-06-20 12:17:58] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/organisations/1/suspend","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:17:58.409276Z"} 
[2025-06-20 12:17:58] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/organisations/1/suspend","status_code":200,"duration_ms":2.03,"response_size":416,"timestamp":"2025-06-20T12:17:58.411219Z"} 
[2025-06-20 12:17:58] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/organisations/1/suspend","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:17:58.421225Z"} 
[2025-06-20 12:17:58] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/organisations/1/suspend","status_code":422,"duration_ms":1.74,"response_size":165,"timestamp":"2025-06-20T12:17:58.422888Z"} 
[2025-06-20 12:17:58] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/organisations","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:17:58.432876Z"} 
[2025-06-20 12:17:58] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/organisations","status_code":401,"duration_ms":0.71,"response_size":145,"timestamp":"2025-06-20T12:17:58.433514Z"} 
[2025-06-20 12:17:58] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/organisations","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:17:58.434207Z"} 
[2025-06-20 12:17:58] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/organisations","status_code":401,"duration_ms":0.29,"response_size":145,"timestamp":"2025-06-20T12:17:58.434489Z"} 
[2025-06-20 12:17:58] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/organisations/1","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:17:58.435080Z"} 
[2025-06-20 12:17:58] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/organisations/1","status_code":401,"duration_ms":0.28,"response_size":145,"timestamp":"2025-06-20T12:17:58.435359Z"} 
[2025-06-20 12:17:58] testing.INFO: API Request {"method":"PUT","url":"http://localhost/api/v1/organisations/1","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:17:58.435934Z"} 
[2025-06-20 12:17:58] testing.INFO: API Response {"method":"PUT","url":"http://localhost/api/v1/organisations/1","status_code":401,"duration_ms":0.28,"response_size":145,"timestamp":"2025-06-20T12:17:58.436209Z"} 
[2025-06-20 12:17:58] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/organisations/1/suspend","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:17:58.436788Z"} 
[2025-06-20 12:17:58] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/organisations/1/suspend","status_code":401,"duration_ms":0.26,"response_size":145,"timestamp":"2025-06-20T12:17:58.437049Z"} 
[2025-06-20 12:17:58] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/organisations/1","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:17:58.448214Z"} 
[2025-06-20 12:17:58] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/organisations/1","status_code":200,"duration_ms":2.3,"response_size":442,"timestamp":"2025-06-20T12:17:58.450463Z"} 
[2025-06-20 12:17:58] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/organisations/2","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:17:58.462109Z"} 
[2025-06-20 12:17:58] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/organisations/2","status_code":403,"duration_ms":2.03,"response_size":134,"timestamp":"2025-06-20T12:17:58.464020Z"} 
[2025-06-20 12:17:58] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/organisations/1","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:17:58.474704Z"} 
[2025-06-20 12:17:58] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/organisations/1","status_code":403,"duration_ms":1.94,"response_size":134,"timestamp":"2025-06-20T12:17:58.476581Z"} 
[2025-06-20 12:17:58] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:17:58.497146Z"} 
[2025-06-20 12:17:58] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/roles","status_code":200,"duration_ms":1.81,"response_size":417,"timestamp":"2025-06-20T12:17:58.498886Z"} 
[2025-06-20 12:17:58] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:17:58.512965Z"} 
[2025-06-20 12:17:58] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/roles","status_code":200,"duration_ms":1.76,"response_size":417,"timestamp":"2025-06-20T12:17:58.514652Z"} 
[2025-06-20 12:17:58] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:17:58.528604Z"} 
[2025-06-20 12:17:58] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/roles","status_code":200,"duration_ms":1.93,"response_size":407,"timestamp":"2025-06-20T12:17:58.530432Z"} 
[2025-06-20 12:17:58] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:17:58.544415Z"} 
[2025-06-20 12:17:58] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/roles","status_code":200,"duration_ms":1.67,"response_size":407,"timestamp":"2025-06-20T12:17:58.546028Z"} 
[2025-06-20 12:17:58] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:17:58.560334Z"} 
[2025-06-20 12:17:58] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/roles","status_code":403,"duration_ms":0.94,"response_size":142,"timestamp":"2025-06-20T12:17:58.561199Z"} 
[2025-06-20 12:17:58] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:17:58.575118Z"} 
[2025-06-20 12:17:58] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/roles","status_code":403,"duration_ms":1.05,"response_size":142,"timestamp":"2025-06-20T12:17:58.576078Z"} 
[2025-06-20 12:17:58] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:17:58.589814Z"} 
[2025-06-20 12:17:58] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/roles","status_code":401,"duration_ms":0.8,"response_size":145,"timestamp":"2025-06-20T12:17:58.590519Z"} 
[2025-06-20 12:17:58] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:17:58.604230Z"} 
[2025-06-20 12:17:58] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/roles","status_code":403,"duration_ms":0.94,"response_size":142,"timestamp":"2025-06-20T12:17:58.605094Z"} 
[2025-06-20 12:17:58] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:17:58.626120Z"} 
[2025-06-20 12:17:58] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/roles","status_code":200,"duration_ms":1.86,"response_size":417,"timestamp":"2025-06-20T12:17:58.627866Z"} 
[2025-06-20 12:17:58] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:17:58.641637Z"} 
[2025-06-20 12:17:58] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/roles","status_code":200,"duration_ms":1.68,"response_size":417,"timestamp":"2025-06-20T12:17:58.643249Z"} 
[2025-06-20 12:17:58] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:17:58.657572Z"} 
[2025-06-20 12:17:58] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/roles","status_code":403,"duration_ms":0.98,"response_size":142,"timestamp":"2025-06-20T12:17:58.658466Z"} 
[2025-06-20 12:17:58] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:17:58.672954Z"} 
[2025-06-20 12:17:58] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/roles","status_code":200,"duration_ms":1.7,"response_size":407,"timestamp":"2025-06-20T12:17:58.674590Z"} 
[2025-06-20 12:17:58] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["59"],"content-type":["application/json"]},"body":{"name":"test-role","organisation_id":1,"guard_name":"api"},"timestamp":"2025-06-20T12:17:58.688581Z"} 
[2025-06-20 12:17:58] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/roles","status_code":201,"duration_ms":2.36,"response_size":256,"timestamp":"2025-06-20T12:17:58.690866Z"} 
[2025-06-20 12:17:58] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["49"],"content-type":["application/json"]},"body":{"name":"test-system-role","guard_name":"system"},"timestamp":"2025-06-20T12:17:58.704856Z"} 
[2025-06-20 12:17:58] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/roles","status_code":201,"duration_ms":2.07,"response_size":269,"timestamp":"2025-06-20T12:17:58.706851Z"} 
[2025-06-20 12:17:58] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["65"],"content-type":["application/json"]},"body":{"name":"invalid-role","organisation_id":1,"guard_name":"system"},"timestamp":"2025-06-20T12:17:58.720578Z"} 
[2025-06-20 12:17:58] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/roles","status_code":422,"duration_ms":1.81,"response_size":173,"timestamp":"2025-06-20T12:17:58.722317Z"} 
[2025-06-20 12:17:58] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/roles/3","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:17:58.736795Z"} 
[2025-06-20 12:17:58] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/roles/3","status_code":200,"duration_ms":1.52,"response_size":254,"timestamp":"2025-06-20T12:17:58.738230Z"} 
[2025-06-20 12:17:58] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:17:58.752370Z"} 
[2025-06-20 12:17:58] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/roles","status_code":401,"duration_ms":0.72,"response_size":145,"timestamp":"2025-06-20T12:17:58.753021Z"} 
[2025-06-20 12:17:58] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:17:58.767570Z"} 
[2025-06-20 12:17:58] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/roles","status_code":200,"duration_ms":1.63,"response_size":417,"timestamp":"2025-06-20T12:17:58.769130Z"} 
[2025-06-20 12:17:58] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:17:58.782823Z"} 
[2025-06-20 12:17:58] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/roles","status_code":403,"duration_ms":0.99,"response_size":142,"timestamp":"2025-06-20T12:17:58.783749Z"} 
[2025-06-20 12:17:58] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/users","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:17:58.806894Z"} 
[2025-06-20 12:17:58] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/users","status_code":200,"duration_ms":3.16,"response_size":2170,"timestamp":"2025-06-20T12:17:58.809983Z"} 
[2025-06-20 12:17:58] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/users","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:17:58.824936Z"} 
[2025-06-20 12:17:58] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/users","status_code":200,"duration_ms":2.77,"response_size":2195,"timestamp":"2025-06-20T12:17:58.827654Z"} 
[2025-06-20 12:17:58] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/users","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:17:58.842137Z"} 
[2025-06-20 12:17:58] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/users","status_code":200,"duration_ms":2.51,"response_size":776,"timestamp":"2025-06-20T12:17:58.844572Z"} 
[2025-06-20 12:17:58] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/users?organisation_id=1","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":{"organisation_id":"1"},"timestamp":"2025-06-20T12:17:58.858977Z"} 
[2025-06-20 12:17:58] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/users?organisation_id=1","status_code":200,"duration_ms":10.49,"response_size":776,"timestamp":"2025-06-20T12:17:58.869428Z"} 
[2025-06-20 12:17:58] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/users?organisation_id=2","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":{"organisation_id":"2"},"timestamp":"2025-06-20T12:17:58.883756Z"} 
[2025-06-20 12:17:58] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/users?organisation_id=2","status_code":422,"duration_ms":1.57,"response_size":197,"timestamp":"2025-06-20T12:17:58.885260Z"} 
[2025-06-20 12:17:58] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/users","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:17:58.899526Z"} 
[2025-06-20 12:17:58] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/users","status_code":200,"duration_ms":2.6,"response_size":766,"timestamp":"2025-06-20T12:17:58.902051Z"} 
[2025-06-20 12:17:58] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/users","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:17:58.916474Z"} 
[2025-06-20 12:17:58] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/users","status_code":403,"duration_ms":1.13,"response_size":134,"timestamp":"2025-06-20T12:17:58.917533Z"} 
[2025-06-20 12:17:58] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/users/6","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:17:58.931921Z"} 
[2025-06-20 12:17:58] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/users/6","status_code":200,"duration_ms":1.59,"response_size":397,"timestamp":"2025-06-20T12:17:58.933427Z"} 
[2025-06-20 12:17:58] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/users/6","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:17:58.947850Z"} 
[2025-06-20 12:17:58] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/users/6","status_code":200,"duration_ms":1.77,"response_size":404,"timestamp":"2025-06-20T12:17:58.949554Z"} 
[2025-06-20 12:17:58] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/users/7","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:17:58.963630Z"} 
[2025-06-20 12:17:58] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/users/7","status_code":403,"duration_ms":1.34,"response_size":134,"timestamp":"2025-06-20T12:17:58.964906Z"} 
[2025-06-20 12:17:58] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/users/4","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:17:58.979221Z"} 
[2025-06-20 12:17:58] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/users/4","status_code":200,"duration_ms":1.79,"response_size":398,"timestamp":"2025-06-20T12:17:58.980942Z"} 
[2025-06-20 12:17:58] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/users/6","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:17:58.995980Z"} 
[2025-06-20 12:17:58] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/users/6","status_code":403,"duration_ms":1.38,"response_size":134,"timestamp":"2025-06-20T12:17:58.997292Z"} 
[2025-06-20 12:17:59] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["99"],"content-type":["application/json"]},"body":{"name":"New User","email":"<EMAIL>","password":"***FILTERED***","organisation_ids":[1,2]},"timestamp":"2025-06-20T12:17:59.011729Z"} 
[2025-06-20 12:17:59] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users","status_code":201,"duration_ms":3.71,"response_size":427,"timestamp":"2025-06-20T12:17:59.015369Z"} 
[2025-06-20 12:17:59] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["104"],"content-type":["application/json"]},"body":{"name":"New Org User","email":"<EMAIL>","password":"***FILTERED***","organisation_ids":[1]},"timestamp":"2025-06-20T12:17:59.029947Z"} 
[2025-06-20 12:17:59] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users","status_code":201,"duration_ms":3.58,"response_size":363,"timestamp":"2025-06-20T12:17:59.033460Z"} 
[2025-06-20 12:17:59] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["101"],"content-type":["application/json"]},"body":{"name":"Invalid User","email":"<EMAIL>","password":"***FILTERED***","organisation_ids":[2]},"timestamp":"2025-06-20T12:17:59.047842Z"} 
[2025-06-20 12:17:59] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users","status_code":422,"duration_ms":2.11,"response_size":215,"timestamp":"2025-06-20T12:17:59.049886Z"} 
[2025-06-20 12:17:59] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["85"],"content-type":["application/json"]},"body":{"name":"Default Org User","email":"<EMAIL>","password":"***FILTERED***"},"timestamp":"2025-06-20T12:17:59.064232Z"} 
[2025-06-20 12:17:59] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users","status_code":201,"duration_ms":3.58,"response_size":370,"timestamp":"2025-06-20T12:17:59.067740Z"} 
[2025-06-20 12:17:59] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["88"],"content-type":["application/json"]},"body":{"name":"Unauthorized User","email":"<EMAIL>","password":"***FILTERED***"},"timestamp":"2025-06-20T12:17:59.082757Z"} 
[2025-06-20 12:17:59] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users","status_code":403,"duration_ms":1.09,"response_size":134,"timestamp":"2025-06-20T12:17:59.083772Z"} 
[2025-06-20 12:17:59] testing.INFO: API Request {"method":"PUT","url":"http://localhost/api/v1/users/6","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["23"],"content-type":["application/json"]},"body":{"name":"Updated Name"},"timestamp":"2025-06-20T12:17:59.097809Z"} 
[2025-06-20 12:17:59] testing.INFO: API Response {"method":"PUT","url":"http://localhost/api/v1/users/6","status_code":200,"duration_ms":2.32,"response_size":387,"timestamp":"2025-06-20T12:17:59.100060Z"} 
[2025-06-20 12:17:59] testing.INFO: API Request {"method":"PUT","url":"http://localhost/api/v1/users/6","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["30"],"content-type":["application/json"]},"body":{"name":"Updated Member Name"},"timestamp":"2025-06-20T12:17:59.116903Z"} 
[2025-06-20 12:17:59] testing.INFO: API Response {"method":"PUT","url":"http://localhost/api/v1/users/6","status_code":200,"duration_ms":2.54,"response_size":397,"timestamp":"2025-06-20T12:17:59.119363Z"} 
[2025-06-20 12:17:59] testing.INFO: API Request {"method":"PUT","url":"http://localhost/api/v1/users/7","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["28"],"content-type":["application/json"]},"body":{"name":"Should Not Update"},"timestamp":"2025-06-20T12:17:59.134092Z"} 
[2025-06-20 12:17:59] testing.INFO: API Response {"method":"PUT","url":"http://localhost/api/v1/users/7","status_code":403,"duration_ms":1.48,"response_size":134,"timestamp":"2025-06-20T12:17:59.135484Z"} 
[2025-06-20 12:17:59] testing.INFO: API Request {"method":"PUT","url":"http://localhost/api/v1/users/6","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["30"],"content-type":["application/json"]},"body":{"name":"Unauthorized Update"},"timestamp":"2025-06-20T12:17:59.149773Z"} 
[2025-06-20 12:17:59] testing.INFO: API Response {"method":"PUT","url":"http://localhost/api/v1/users/6","status_code":403,"duration_ms":1.43,"response_size":134,"timestamp":"2025-06-20T12:17:59.151132Z"} 
[2025-06-20 12:17:59] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/6/suspend","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:17:59.165722Z"} 
[2025-06-20 12:17:59] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/6/suspend","status_code":200,"duration_ms":1.79,"response_size":391,"timestamp":"2025-06-20T12:17:59.167449Z"} 
[2025-06-20 12:17:59] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/6/suspend","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:17:59.190028Z"} 
[2025-06-20 12:17:59] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/6/suspend","status_code":200,"duration_ms":1.96,"response_size":394,"timestamp":"2025-06-20T12:17:59.191889Z"} 
[2025-06-20 12:17:59] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/7/suspend","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:17:59.206217Z"} 
[2025-06-20 12:17:59] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/7/suspend","status_code":403,"duration_ms":1.34,"response_size":134,"timestamp":"2025-06-20T12:17:59.207491Z"} 
[2025-06-20 12:17:59] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/6/activate","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:17:59.222170Z"} 
[2025-06-20 12:17:59] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/6/activate","status_code":200,"duration_ms":1.56,"response_size":398,"timestamp":"2025-06-20T12:17:59.223648Z"} 
[2025-06-20 12:17:59] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/6/activate","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:17:59.237756Z"} 
[2025-06-20 12:17:59] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/6/activate","status_code":200,"duration_ms":1.83,"response_size":391,"timestamp":"2025-06-20T12:17:59.239526Z"} 
[2025-06-20 12:17:59] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/7/activate","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:17:59.254389Z"} 
[2025-06-20 12:17:59] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/7/activate","status_code":403,"duration_ms":1.44,"response_size":134,"timestamp":"2025-06-20T12:17:59.255750Z"} 
[2025-06-20 12:17:59] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/6/suspend","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:17:59.270167Z"} 
[2025-06-20 12:17:59] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/6/suspend","status_code":403,"duration_ms":1.44,"response_size":134,"timestamp":"2025-06-20T12:17:59.271533Z"} 
[2025-06-20 12:17:59] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/6/activate","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:17:59.285737Z"} 
[2025-06-20 12:17:59] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/6/activate","status_code":403,"duration_ms":1.35,"response_size":134,"timestamp":"2025-06-20T12:17:59.287019Z"} 
[2025-06-20 12:17:59] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/users","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:17:59.308727Z"} 
[2025-06-20 12:17:59] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/users","status_code":200,"duration_ms":2.91,"response_size":1206,"timestamp":"2025-06-20T12:17:59.311562Z"} 
[2025-06-20 12:17:59] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/users?organisation_id=1","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":{"organisation_id":"1"},"timestamp":"2025-06-20T12:17:59.323954Z"} 
[2025-06-20 12:17:59] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/users?organisation_id=1","status_code":200,"duration_ms":3.22,"response_size":842,"timestamp":"2025-06-20T12:17:59.327098Z"} 
[2025-06-20 12:17:59] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["99"],"content-type":["application/json"]},"body":{"name":"New User","email":"<EMAIL>","password":"***FILTERED***","organisation_ids":[1,2]},"timestamp":"2025-06-20T12:17:59.337006Z"} 
[2025-06-20 12:17:59] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users","status_code":201,"duration_ms":3.92,"response_size":435,"timestamp":"2025-06-20T12:17:59.340847Z"} 
[2025-06-20 12:17:59] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["76"],"content-type":["application/json"]},"body":{"name":"Simple User","email":"<EMAIL>","password":"***FILTERED***"},"timestamp":"2025-06-20T12:17:59.350411Z"} 
[2025-06-20 12:17:59] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users","status_code":201,"duration_ms":3.24,"response_size":292,"timestamp":"2025-06-20T12:17:59.353588Z"} 
[2025-06-20 12:17:59] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/register","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["103"],"content-type":["application/json"]},"body":{"name":"Guest User","email":"<EMAIL>","password":"***FILTERED***","verification_code":"625740"},"timestamp":"2025-06-20T12:17:59.363311Z"} 
[2025-06-20 12:17:59] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/register","status_code":201,"duration_ms":2.52,"response_size":293,"timestamp":"2025-06-20T12:17:59.365791Z"} 
[2025-06-20 12:17:59] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/register","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["103"],"content-type":["application/json"]},"body":{"name":"Guest User","email":"<EMAIL>","password":"***FILTERED***","verification_code":"123456"},"timestamp":"2025-06-20T12:17:59.375957Z"} 
[2025-06-20 12:17:59] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/register","status_code":422,"duration_ms":1.29,"response_size":194,"timestamp":"2025-06-20T12:17:59.377184Z"} 
[2025-06-20 12:17:59] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/register","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["74"],"content-type":["application/json"]},"body":{"name":"Guest User","email":"<EMAIL>","password":"***FILTERED***"},"timestamp":"2025-06-20T12:17:59.386428Z"} 
[2025-06-20 12:17:59] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/register","status_code":422,"duration_ms":1.29,"response_size":186,"timestamp":"2025-06-20T12:17:59.387654Z"} 
[2025-06-20 12:17:59] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/register","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["126"],"content-type":["application/json"]},"body":{"name":"Guest User","email":"<EMAIL>","password":"***FILTERED***","verification_code":"621805","organisation_ids":[1]},"timestamp":"2025-06-20T12:17:59.407060Z"} 
[2025-06-20 12:17:59] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/register","status_code":422,"duration_ms":1.19,"response_size":218,"timestamp":"2025-06-20T12:17:59.408195Z"} 
[2025-06-20 12:17:59] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:17:59.418559Z"} 
[2025-06-20 12:17:59] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users","status_code":422,"duration_ms":1.98,"response_size":305,"timestamp":"2025-06-20T12:17:59.420468Z"} 
[2025-06-20 12:17:59] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["77"],"content-type":["application/json"]},"body":{"name":"Test User","email":"<EMAIL>","password":"***FILTERED***"},"timestamp":"2025-06-20T12:17:59.431324Z"} 
[2025-06-20 12:17:59] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users","status_code":422,"duration_ms":2.96,"response_size":199,"timestamp":"2025-06-20T12:17:59.434209Z"} 
[2025-06-20 12:17:59] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["102"],"content-type":["application/json"]},"body":{"name":"Test User","email":"<EMAIL>","password":"***FILTERED***","organisation_ids":[999,1000]},"timestamp":"2025-06-20T12:17:59.446025Z"} 
[2025-06-20 12:17:59] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users","status_code":422,"duration_ms":2.91,"response_size":280,"timestamp":"2025-06-20T12:17:59.448832Z"} 
[2025-06-20 12:17:59] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/users/3","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:17:59.459921Z"} 
[2025-06-20 12:17:59] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/users/3","status_code":200,"duration_ms":2.27,"response_size":468,"timestamp":"2025-06-20T12:17:59.462111Z"} 
[2025-06-20 12:17:59] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/users/999","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:17:59.472349Z"} 
[2025-06-20 12:17:59] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/users/999","status_code":404,"duration_ms":1.4,"response_size":135,"timestamp":"2025-06-20T12:17:59.473700Z"} 
[2025-06-20 12:17:59] testing.INFO: API Request {"method":"PUT","url":"http://localhost/api/v1/users/3","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["42"],"content-type":["application/json"]},"body":{"name":"New Name","organisation_ids":[2]},"timestamp":"2025-06-20T12:17:59.483834Z"} 
[2025-06-20 12:17:59] testing.INFO: API Response {"method":"PUT","url":"http://localhost/api/v1/users/3","status_code":200,"duration_ms":4.1,"response_size":383,"timestamp":"2025-06-20T12:17:59.487869Z"} 
[2025-06-20 12:17:59] testing.INFO: API Request {"method":"PUT","url":"http://localhost/api/v1/users/999","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["19"],"content-type":["application/json"]},"body":{"name":"New Name"},"timestamp":"2025-06-20T12:17:59.497731Z"} 
[2025-06-20 12:17:59] testing.INFO: API Response {"method":"PUT","url":"http://localhost/api/v1/users/999","status_code":404,"duration_ms":1.43,"response_size":135,"timestamp":"2025-06-20T12:17:59.499093Z"} 
[2025-06-20 12:17:59] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/3/organisations/1","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:17:59.508912Z"} 
[2025-06-20 12:17:59] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/3/organisations/1","status_code":200,"duration_ms":2.46,"response_size":395,"timestamp":"2025-06-20T12:17:59.511287Z"} 
[2025-06-20 12:17:59] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/3/organisations/1","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:17:59.522064Z"} 
[2025-06-20 12:17:59] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/3/organisations/1","status_code":200,"duration_ms":2.37,"response_size":394,"timestamp":"2025-06-20T12:17:59.524346Z"} 
[2025-06-20 12:17:59] testing.INFO: API Request {"method":"DELETE","url":"http://localhost/api/v1/users/3/organisations/1","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:17:59.535247Z"} 
[2025-06-20 12:17:59] testing.INFO: API Response {"method":"DELETE","url":"http://localhost/api/v1/users/3/organisations/1","status_code":200,"duration_ms":3.11,"response_size":405,"timestamp":"2025-06-20T12:17:59.538293Z"} 
[2025-06-20 12:17:59] testing.INFO: API Request {"method":"PUT","url":"http://localhost/api/v1/users/3/organisations","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["24"],"content-type":["application/json"]},"body":{"organisation_ids":[2]},"timestamp":"2025-06-20T12:17:59.549008Z"} 
[2025-06-20 12:17:59] testing.INFO: API Response {"method":"PUT","url":"http://localhost/api/v1/users/3/organisations","status_code":200,"duration_ms":3.39,"response_size":405,"timestamp":"2025-06-20T12:17:59.552332Z"} 
[2025-06-20 12:17:59] testing.INFO: API Request {"method":"PUT","url":"http://localhost/api/v1/users/3/organisations","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["23"],"content-type":["application/json"]},"body":{"organisation_ids":[]},"timestamp":"2025-06-20T12:17:59.562579Z"} 
[2025-06-20 12:17:59] testing.INFO: API Response {"method":"PUT","url":"http://localhost/api/v1/users/3/organisations","status_code":200,"duration_ms":3.98,"response_size":333,"timestamp":"2025-06-20T12:17:59.566495Z"} 
[2025-06-20 12:17:59] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/3/suspend","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:17:59.577040Z"} 
[2025-06-20 12:17:59] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/3/suspend","status_code":200,"duration_ms":2.24,"response_size":334,"timestamp":"2025-06-20T12:17:59.579212Z"} 
[2025-06-20 12:17:59] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/users","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:17:59.589165Z"} 
[2025-06-20 12:17:59] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/users","status_code":401,"duration_ms":0.72,"response_size":145,"timestamp":"2025-06-20T12:17:59.589819Z"} 
[2025-06-20 12:17:59] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:17:59.590553Z"} 
[2025-06-20 12:17:59] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users","status_code":401,"duration_ms":0.3,"response_size":145,"timestamp":"2025-06-20T12:17:59.590852Z"} 
[2025-06-20 12:17:59] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/users/3","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:17:59.591474Z"} 
[2025-06-20 12:17:59] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/users/3","status_code":401,"duration_ms":0.26,"response_size":145,"timestamp":"2025-06-20T12:17:59.591732Z"} 
[2025-06-20 12:17:59] testing.INFO: API Request {"method":"PUT","url":"http://localhost/api/v1/users/3","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:17:59.592321Z"} 
[2025-06-20 12:17:59] testing.INFO: API Response {"method":"PUT","url":"http://localhost/api/v1/users/3","status_code":401,"duration_ms":0.26,"response_size":145,"timestamp":"2025-06-20T12:17:59.592575Z"} 
[2025-06-20 12:17:59] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/3/suspend","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:17:59.593152Z"} 
[2025-06-20 12:17:59] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/3/suspend","status_code":401,"duration_ms":0.27,"response_size":145,"timestamp":"2025-06-20T12:17:59.593420Z"} 
[2025-06-20 12:17:59] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/3/organisations/1","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:17:59.594056Z"} 
[2025-06-20 12:17:59] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/3/organisations/1","status_code":401,"duration_ms":0.28,"response_size":145,"timestamp":"2025-06-20T12:17:59.594327Z"} 
[2025-06-20 12:17:59] testing.INFO: API Request {"method":"DELETE","url":"http://localhost/api/v1/users/3/organisations/1","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:17:59.594928Z"} 
[2025-06-20 12:17:59] testing.INFO: API Response {"method":"DELETE","url":"http://localhost/api/v1/users/3/organisations/1","status_code":401,"duration_ms":0.26,"response_size":145,"timestamp":"2025-06-20T12:17:59.595185Z"} 
[2025-06-20 12:17:59] testing.INFO: API Request {"method":"PUT","url":"http://localhost/api/v1/users/3/organisations","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:17:59.595765Z"} 
[2025-06-20 12:17:59] testing.INFO: API Response {"method":"PUT","url":"http://localhost/api/v1/users/3/organisations","status_code":401,"duration_ms":0.25,"response_size":145,"timestamp":"2025-06-20T12:17:59.596014Z"} 
[2025-06-20 12:17:59] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/send-verification-code","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["28"],"content-type":["application/json"]},"body":{"email":"<EMAIL>"},"timestamp":"2025-06-20T12:17:59.605321Z"} 
[2025-06-20 12:17:59] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/send-verification-code","status_code":200,"duration_ms":1.26,"response_size":197,"timestamp":"2025-06-20T12:17:59.606491Z"} 
[2025-06-20 12:17:59] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/send-verification-code","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["25"],"content-type":["application/json"]},"body":{"email":"invalid-email"},"timestamp":"2025-06-20T12:17:59.615662Z"} 
[2025-06-20 12:17:59] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/send-verification-code","status_code":422,"duration_ms":0.96,"response_size":176,"timestamp":"2025-06-20T12:17:59.616569Z"} 
[2025-06-20 12:17:59] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/send-verification-code","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["32"],"content-type":["application/json"]},"body":{"email":"<EMAIL>"},"timestamp":"2025-06-20T12:17:59.626910Z"} 
[2025-06-20 12:17:59] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/send-verification-code","status_code":422,"duration_ms":11.01,"response_size":181,"timestamp":"2025-06-20T12:17:59.637825Z"} 
[2025-06-20 12:17:59] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/send-verification-code","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["28"],"content-type":["application/json"]},"body":{"email":"<EMAIL>"},"timestamp":"2025-06-20T12:17:59.647388Z"} 
[2025-06-20 12:17:59] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/send-verification-code","status_code":422,"duration_ms":0.76,"response_size":185,"timestamp":"2025-06-20T12:17:59.648093Z"} 
[2025-06-20 12:17:59] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/6/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["13"],"content-type":["application/json"]},"body":{"role_id":4},"timestamp":"2025-06-20T12:17:59.669754Z"} 
[2025-06-20 12:17:59] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/6/roles","status_code":200,"duration_ms":2.39,"response_size":170,"timestamp":"2025-06-20T12:17:59.672063Z"} 
[2025-06-20 12:17:59] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/6/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["13"],"content-type":["application/json"]},"body":{"role_id":4},"timestamp":"2025-06-20T12:17:59.684318Z"} 
[2025-06-20 12:17:59] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/6/roles","status_code":200,"duration_ms":2.35,"response_size":170,"timestamp":"2025-06-20T12:17:59.686551Z"} 
[2025-06-20 12:17:59] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/6/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["13"],"content-type":["application/json"]},"body":{"role_id":4},"timestamp":"2025-06-20T12:17:59.698627Z"} 
[2025-06-20 12:17:59] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/6/roles","status_code":200,"duration_ms":2.92,"response_size":170,"timestamp":"2025-06-20T12:17:59.701484Z"} 
[2025-06-20 12:17:59] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/6/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["13"],"content-type":["application/json"]},"body":{"role_id":3},"timestamp":"2025-06-20T12:17:59.713405Z"} 
[2025-06-20 12:17:59] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/6/roles","status_code":422,"duration_ms":2.17,"response_size":281,"timestamp":"2025-06-20T12:17:59.715510Z"} 
[2025-06-20 12:17:59] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/6/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["13"],"content-type":["application/json"]},"body":{"role_id":4},"timestamp":"2025-06-20T12:17:59.727962Z"} 
[2025-06-20 12:17:59] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/6/roles","status_code":403,"duration_ms":2.28,"response_size":134,"timestamp":"2025-06-20T12:17:59.730178Z"} 
[2025-06-20 12:17:59] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/6/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["13"],"content-type":["application/json"]},"body":{"role_id":3},"timestamp":"2025-06-20T12:17:59.742482Z"} 
[2025-06-20 12:17:59] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/6/roles","status_code":422,"duration_ms":2.21,"response_size":281,"timestamp":"2025-06-20T12:17:59.744625Z"} 
[2025-06-20 12:17:59] testing.INFO: API Request {"method":"PUT","url":"http://localhost/api/v1/users/6/transfer-owner","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["21"],"content-type":["application/json"]},"body":{"organisation_id":1},"timestamp":"2025-06-20T12:17:59.756791Z"} 
[2025-06-20 12:17:59] testing.INFO: API Response {"method":"PUT","url":"http://localhost/api/v1/users/6/transfer-owner","status_code":200,"duration_ms":3.22,"response_size":174,"timestamp":"2025-06-20T12:17:59.759947Z"} 
[2025-06-20 12:17:59] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/user","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:17:59.771747Z"} 
[2025-06-20 12:17:59] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/user","status_code":200,"duration_ms":1.43,"response_size":562,"timestamp":"2025-06-20T12:17:59.773115Z"} 
[2025-06-20 12:17:59] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/users/assignable-roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:17:59.784897Z"} 
[2025-06-20 12:17:59] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/users/assignable-roles","status_code":200,"duration_ms":1.74,"response_size":142,"timestamp":"2025-06-20T12:17:59.786572Z"} 
[2025-06-20 12:17:59] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/health","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:17:59.797982Z"} 
[2025-06-20 12:17:59] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/health","status_code":200,"duration_ms":19.66,"response_size":263,"timestamp":"2025-06-20T12:17:59.817581Z"} 
[2025-06-20 12:17:59] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/status","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:17:59.824014Z"} 
[2025-06-20 12:17:59] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/status","status_code":200,"duration_ms":0.56,"response_size":390,"timestamp":"2025-06-20T12:17:59.824505Z"} 
[2025-06-20 12:17:59] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/health","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:17:59.830125Z"} 
[2025-06-20 12:17:59] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/health","status_code":200,"duration_ms":1.11,"response_size":263,"timestamp":"2025-06-20T12:17:59.831173Z"} 
[2025-06-20 12:17:59] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/status","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:17:59.843308Z"} 
[2025-06-20 12:17:59] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/status","status_code":200,"duration_ms":0.58,"response_size":390,"timestamp":"2025-06-20T12:17:59.843820Z"} 
[2025-06-20 12:17:59] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/status","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:17:59.849380Z"} 
[2025-06-20 12:17:59] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/status","status_code":200,"duration_ms":0.52,"response_size":390,"timestamp":"2025-06-20T12:17:59.849851Z"} 
[2025-06-20 12:20:38] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/6/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["13"],"content-type":["application/json"]},"body":{"role_id":4},"timestamp":"2025-06-20T12:20:38.254453Z"} 
[2025-06-20 12:20:38] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/6/roles","status_code":200,"duration_ms":30.44,"response_size":170,"timestamp":"2025-06-20T12:20:38.277157Z"} 
[2025-06-20 12:20:38] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/6/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["13"],"content-type":["application/json"]},"body":{"role_id":4},"timestamp":"2025-06-20T12:20:38.302071Z"} 
[2025-06-20 12:20:38] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/6/roles","status_code":200,"duration_ms":2.46,"response_size":170,"timestamp":"2025-06-20T12:20:38.304434Z"} 
[2025-06-20 12:20:38] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/6/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["13"],"content-type":["application/json"]},"body":{"role_id":4},"timestamp":"2025-06-20T12:20:38.317625Z"} 
[2025-06-20 12:20:38] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/6/roles","status_code":200,"duration_ms":3.17,"response_size":170,"timestamp":"2025-06-20T12:20:38.320736Z"} 
[2025-06-20 12:20:38] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/6/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["13"],"content-type":["application/json"]},"body":{"role_id":3},"timestamp":"2025-06-20T12:20:38.335374Z"} 
[2025-06-20 12:20:38] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/6/roles","status_code":422,"duration_ms":3.94,"response_size":281,"timestamp":"2025-06-20T12:20:38.339249Z"} 
[2025-06-20 12:20:38] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/6/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["13"],"content-type":["application/json"]},"body":{"role_id":4},"timestamp":"2025-06-20T12:20:38.351572Z"} 
[2025-06-20 12:20:38] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/6/roles","status_code":403,"duration_ms":4.33,"response_size":134,"timestamp":"2025-06-20T12:20:38.355842Z"} 
[2025-06-20 12:20:38] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/6/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["13"],"content-type":["application/json"]},"body":{"role_id":3},"timestamp":"2025-06-20T12:20:38.367386Z"} 
[2025-06-20 12:20:38] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/6/roles","status_code":422,"duration_ms":2.04,"response_size":281,"timestamp":"2025-06-20T12:20:38.369367Z"} 
[2025-06-20 12:20:38] testing.INFO: API Request {"method":"PUT","url":"http://localhost/api/v1/users/6/transfer-owner","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["21"],"content-type":["application/json"]},"body":{"organisation_id":1},"timestamp":"2025-06-20T12:20:38.382455Z"} 
[2025-06-20 12:20:38] testing.INFO: API Response {"method":"PUT","url":"http://localhost/api/v1/users/6/transfer-owner","status_code":200,"duration_ms":3.3,"response_size":174,"timestamp":"2025-06-20T12:20:38.385696Z"} 
[2025-06-20 12:20:38] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/user","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:20:38.400414Z"} 
[2025-06-20 12:20:38] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/user","status_code":200,"duration_ms":3.84,"response_size":556,"timestamp":"2025-06-20T12:20:38.404193Z"} 
[2025-06-20 12:20:38] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/users/assignable-roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:20:38.416954Z"} 
[2025-06-20 12:20:38] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/users/assignable-roles","status_code":200,"duration_ms":1.86,"response_size":142,"timestamp":"2025-06-20T12:20:38.418757Z"} 
[2025-06-20 12:35:54] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/auth/login","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["53"],"content-type":["application/json"]},"body":{"email":"<EMAIL>","password":"***FILTERED***"},"timestamp":"2025-06-20T12:35:54.353291Z"} 
[2025-06-20 12:35:54] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/auth/login","status_code":200,"duration_ms":16.79,"response_size":266,"timestamp":"2025-06-20T12:35:54.369714Z"} 
[2025-06-20 12:35:54] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/auth/login","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["55"],"content-type":["application/json"]},"body":{"email":"<EMAIL>","password":"***FILTERED***"},"timestamp":"2025-06-20T12:35:54.379064Z"} 
[2025-06-20 12:35:54] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/auth/login","status_code":422,"duration_ms":2.8,"response_size":184,"timestamp":"2025-06-20T12:35:54.381803Z"} 
[2025-06-20 12:35:54] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/user","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"],"authorization":["***FILTERED***"]},"body":[],"timestamp":"2025-06-20T12:35:54.389421Z"} 
[2025-06-20 12:35:54] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/user","status_code":200,"duration_ms":6.65,"response_size":374,"timestamp":"2025-06-20T12:35:54.396018Z"} 
[2025-06-20 12:35:54] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/user","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:35:54.403749Z"} 
[2025-06-20 12:35:54] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/user","status_code":401,"duration_ms":0.72,"response_size":145,"timestamp":"2025-06-20T12:35:54.404392Z"} 
[2025-06-20 12:35:54] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/auth/login","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["53"],"content-type":["application/json"]},"body":{"email":"<EMAIL>","password":"***FILTERED***"},"timestamp":"2025-06-20T12:35:54.411396Z"} 
[2025-06-20 12:35:54] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/auth/login","status_code":200,"duration_ms":2.0,"response_size":266,"timestamp":"2025-06-20T12:35:54.413334Z"} 
[2025-06-20 12:35:54] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/user","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"],"authorization":["***FILTERED***"]},"body":[],"timestamp":"2025-06-20T12:35:54.413964Z"} 
[2025-06-20 12:35:54] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/user","status_code":200,"duration_ms":1.76,"response_size":374,"timestamp":"2025-06-20T12:35:54.415727Z"} 
[2025-06-20 12:35:54] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/auth/logout","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"],"authorization":["***FILTERED***"]},"body":[],"timestamp":"2025-06-20T12:35:54.416423Z"} 
[2025-06-20 12:35:54] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/auth/logout","status_code":200,"duration_ms":0.35,"response_size":98,"timestamp":"2025-06-20T12:35:54.416774Z"} 
[2025-06-20 12:35:54] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/user","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"],"authorization":["***FILTERED***"]},"body":[],"timestamp":"2025-06-20T12:35:54.424075Z"} 
[2025-06-20 12:35:54] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/user","status_code":200,"duration_ms":1.93,"response_size":374,"timestamp":"2025-06-20T12:35:54.425948Z"} 
[2025-06-20 12:35:54] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/auth/revoke-all-tokens","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"],"authorization":["***FILTERED***"]},"body":[],"timestamp":"2025-06-20T12:35:54.426591Z"} 
[2025-06-20 12:35:54] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/auth/revoke-all-tokens","status_code":200,"duration_ms":0.43,"response_size":99,"timestamp":"2025-06-20T12:35:54.427019Z"} 
[2025-06-20 12:35:54] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/auth/login","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["42"],"content-type":["application/json"]},"body":{"email":"invalid-email","password":"***FILTERED***"},"timestamp":"2025-06-20T12:35:54.434559Z"} 
[2025-06-20 12:35:54] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/auth/login","status_code":422,"duration_ms":2.95,"response_size":276,"timestamp":"2025-06-20T12:35:54.437447Z"} 
[2025-06-20 12:35:54] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/user","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:35:54.455068Z"} 
[2025-06-20 12:35:54] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/user","status_code":401,"duration_ms":0.72,"response_size":145,"timestamp":"2025-06-20T12:35:54.455720Z"} 
[2025-06-20 12:35:54] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:35:54.469404Z"} 
[2025-06-20 12:35:54] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/roles","status_code":403,"duration_ms":1.44,"response_size":142,"timestamp":"2025-06-20T12:35:54.470770Z"} 
[2025-06-20 12:35:54] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/auth/login","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["39"],"content-type":["application/json"]},"body":{"email":"invalid-email","password":null},"timestamp":"2025-06-20T12:35:54.483826Z"} 
[2025-06-20 12:35:54] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/auth/login","status_code":422,"duration_ms":1.05,"response_size":257,"timestamp":"2025-06-20T12:35:54.484811Z"} 
[2025-06-20 12:35:54] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/user","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:35:54.497273Z"} 
[2025-06-20 12:35:54] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/user","status_code":401,"duration_ms":0.46,"response_size":145,"timestamp":"2025-06-20T12:35:54.497668Z"} 
[2025-06-20 12:35:54] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:35:54.513391Z"} 
[2025-06-20 12:35:54] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations","status_code":401,"duration_ms":0.97,"response_size":145,"timestamp":"2025-06-20T12:35:54.514282Z"} 
[2025-06-20 12:35:54] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:35:54.532121Z"} 
[2025-06-20 12:35:54] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations","status_code":200,"duration_ms":4.65,"response_size":1595,"timestamp":"2025-06-20T12:35:54.536676Z"} 
[2025-06-20 12:35:54] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:35:54.547657Z"} 
[2025-06-20 12:35:54] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations","status_code":200,"duration_ms":2.0,"response_size":668,"timestamp":"2025-06-20T12:35:54.549601Z"} 
[2025-06-20 12:35:54] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:35:54.559167Z"} 
[2025-06-20 12:35:54] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations","status_code":403,"duration_ms":1.14,"response_size":134,"timestamp":"2025-06-20T12:35:54.560248Z"} 
[2025-06-20 12:35:54] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations?role=member","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":{"role":"member"},"timestamp":"2025-06-20T12:35:54.571430Z"} 
[2025-06-20 12:35:54] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations?role=member","status_code":200,"duration_ms":2.26,"response_size":1130,"timestamp":"2025-06-20T12:35:54.573616Z"} 
[2025-06-20 12:35:54] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations?model_id=1","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":{"model_id":"1"},"timestamp":"2025-06-20T12:35:54.574330Z"} 
[2025-06-20 12:35:54] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations?model_id=1","status_code":200,"duration_ms":1.79,"response_size":1128,"timestamp":"2025-06-20T12:35:54.576111Z"} 
[2025-06-20 12:35:54] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations?model_type=App%5CModels%5COrganisation","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":{"model_type":"App\\Models\\Organisation"},"timestamp":"2025-06-20T12:35:54.576699Z"} 
[2025-06-20 12:35:54] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations?model_type=App%5CModels%5COrganisation","status_code":200,"duration_ms":1.83,"response_size":1592,"timestamp":"2025-06-20T12:35:54.578530Z"} 
[2025-06-20 12:35:54] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations?status=valid","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":{"status":"valid"},"timestamp":"2025-06-20T12:35:54.589538Z"} 
[2025-06-20 12:35:54] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations?status=valid","status_code":200,"duration_ms":1.82,"response_size":674,"timestamp":"2025-06-20T12:35:54.591286Z"} 
[2025-06-20 12:35:54] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations?status=expired","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":{"status":"expired"},"timestamp":"2025-06-20T12:35:54.591944Z"} 
[2025-06-20 12:35:54] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations?status=expired","status_code":200,"duration_ms":1.52,"response_size":674,"timestamp":"2025-06-20T12:35:54.593447Z"} 
[2025-06-20 12:35:54] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations?status=used_up","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":{"status":"used_up"},"timestamp":"2025-06-20T12:35:54.594050Z"} 
[2025-06-20 12:35:54] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations?status=used_up","status_code":200,"duration_ms":1.47,"response_size":674,"timestamp":"2025-06-20T12:35:54.595530Z"} 
[2025-06-20 12:35:54] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations?per_page=10","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":{"per_page":"10"},"timestamp":"2025-06-20T12:35:54.611930Z"} 
[2025-06-20 12:35:54] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations?per_page=10","status_code":200,"duration_ms":3.31,"response_size":4925,"timestamp":"2025-06-20T12:35:54.615168Z"} 
[2025-06-20 12:35:54] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations?page=2&per_page=10","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":{"per_page":"10","page":"2"},"timestamp":"2025-06-20T12:35:54.615948Z"} 
[2025-06-20 12:35:54] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations?page=2&per_page=10","status_code":200,"duration_ms":3.13,"response_size":4926,"timestamp":"2025-06-20T12:35:54.619078Z"} 
[2025-06-20 12:35:54] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations/01978d56-a285-707e-8709-70b733a7c63d","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:35:54.630057Z"} 
[2025-06-20 12:35:54] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations/01978d56-a285-707e-8709-70b733a7c63d","status_code":200,"duration_ms":1.28,"response_size":586,"timestamp":"2025-06-20T12:35:54.631260Z"} 
[2025-06-20 12:35:54] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations/01978d56-a291-713d-b2da-5c82661c730c","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:35:54.641910Z"} 
[2025-06-20 12:35:54] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations/01978d56-a291-713d-b2da-5c82661c730c","status_code":200,"duration_ms":1.22,"response_size":595,"timestamp":"2025-06-20T12:35:54.643075Z"} 
[2025-06-20 12:35:54] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations/01978d56-a29c-7045-8408-28b345c992ee","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:35:54.653240Z"} 
[2025-06-20 12:35:54] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations/01978d56-a29c-7045-8408-28b345c992ee","status_code":410,"duration_ms":0.76,"response_size":147,"timestamp":"2025-06-20T12:35:54.653944Z"} 
[2025-06-20 12:35:54] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations/01978d56-a2a7-720b-9479-3ebc02b28b94","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:35:54.664639Z"} 
[2025-06-20 12:35:54] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations/01978d56-a2a7-720b-9479-3ebc02b28b94","status_code":410,"duration_ms":0.82,"response_size":171,"timestamp":"2025-06-20T12:35:54.665370Z"} 
[2025-06-20 12:35:54] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations/01978d56-a2b3-7027-b37c-6b7bcb12dadc","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:35:54.675914Z"} 
[2025-06-20 12:35:54] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations/01978d56-a2b3-7027-b37c-6b7bcb12dadc","status_code":200,"duration_ms":1.29,"response_size":603,"timestamp":"2025-06-20T12:35:54.677145Z"} 
[2025-06-20 12:35:54] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations/01978d56-a2be-7260-b194-6061344999eb","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:35:54.687303Z"} 
[2025-06-20 12:35:54] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations/01978d56-a2be-7260-b194-6061344999eb","status_code":200,"duration_ms":1.27,"response_size":585,"timestamp":"2025-06-20T12:35:54.688511Z"} 
[2025-06-20 12:35:54] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/invitations/01978d56-a2cb-706e-bf12-8d1e0f26a817/accept","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:35:54.699949Z"} 
[2025-06-20 12:35:54] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/invitations/01978d56-a2cb-706e-bf12-8d1e0f26a817/accept","status_code":422,"duration_ms":1.75,"response_size":223,"timestamp":"2025-06-20T12:35:54.701636Z"} 
[2025-06-20 12:35:54] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/invitations/01978d56-a2d7-71ca-b809-07abce4be0e9/accept","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:35:54.712585Z"} 
[2025-06-20 12:35:54] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/invitations/01978d56-a2d7-71ca-b809-07abce4be0e9/accept","status_code":401,"duration_ms":0.88,"response_size":145,"timestamp":"2025-06-20T12:35:54.713388Z"} 
[2025-06-20 12:35:54] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/invitations/01978d56-a2e5-7057-adf3-10a7bd22a3cb/accept","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:35:54.726362Z"} 
[2025-06-20 12:35:54] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/invitations/01978d56-a2e5-7057-adf3-10a7bd22a3cb/accept","status_code":200,"duration_ms":3.35,"response_size":462,"timestamp":"2025-06-20T12:35:54.729646Z"} 
[2025-06-20 12:35:54] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/invitations/01978d56-a2f3-7096-89f9-45cd45871466/accept","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:35:54.740561Z"} 
[2025-06-20 12:35:54] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/invitations/01978d56-a2f3-7096-89f9-45cd45871466/accept","status_code":422,"duration_ms":0.74,"response_size":246,"timestamp":"2025-06-20T12:35:54.741241Z"} 
[2025-06-20 12:35:54] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/status","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:35:54.755097Z"} 
[2025-06-20 12:35:54] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/status","status_code":200,"duration_ms":0.76,"response_size":392,"timestamp":"2025-06-20T12:35:54.755764Z"} 
[2025-06-20 12:35:54] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/status","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["zh-CN,zh;q=0.9,en;q=0.8"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:35:54.763237Z"} 
[2025-06-20 12:35:54] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/status","status_code":200,"duration_ms":0.81,"response_size":393,"timestamp":"2025-06-20T12:35:54.763961Z"} 
[2025-06-20 12:35:54] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/status","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"],"x-locale":["zh"]},"body":[],"timestamp":"2025-06-20T12:35:54.769876Z"} 
[2025-06-20 12:35:54] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/status","status_code":200,"duration_ms":0.58,"response_size":393,"timestamp":"2025-06-20T12:35:54.770400Z"} 
[2025-06-20 12:35:54] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/status","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"],"x-locale":["en"]},"body":[],"timestamp":"2025-06-20T12:35:54.776412Z"} 
[2025-06-20 12:35:54] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/status","status_code":200,"duration_ms":0.55,"response_size":392,"timestamp":"2025-06-20T12:35:54.776904Z"} 
[2025-06-20 12:35:54] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/status","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"],"x-locale":["fr"]},"body":[],"timestamp":"2025-06-20T12:35:54.788459Z"} 
[2025-06-20 12:35:54] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/status","status_code":200,"duration_ms":0.63,"response_size":392,"timestamp":"2025-06-20T12:35:54.789022Z"} 
[2025-06-20 12:35:54] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/status","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["zh-CN,zh;q=0.9,en;q=0.8"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"],"x-locale":["en"]},"body":[],"timestamp":"2025-06-20T12:35:54.794901Z"} 
[2025-06-20 12:35:54] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/status","status_code":200,"duration_ms":0.53,"response_size":392,"timestamp":"2025-06-20T12:35:54.795376Z"} 
[2025-06-20 12:35:54] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/user","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"],"x-locale":["en"]},"body":[],"timestamp":"2025-06-20T12:35:54.817981Z"} 
[2025-06-20 12:35:54] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/user","status_code":401,"duration_ms":0.7,"response_size":145,"timestamp":"2025-06-20T12:35:54.818616Z"} 
[2025-06-20 12:35:54] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/user","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"],"x-locale":["zh"]},"body":[],"timestamp":"2025-06-20T12:35:54.824555Z"} 
[2025-06-20 12:35:54] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/user","status_code":401,"duration_ms":0.61,"response_size":158,"timestamp":"2025-06-20T12:35:54.825109Z"} 
[2025-06-20 12:35:54] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/auth/login","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"],"x-locale":["en"]},"body":[],"timestamp":"2025-06-20T12:35:54.830951Z"} 
[2025-06-20 12:35:54] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/auth/login","status_code":422,"duration_ms":0.92,"response_size":245,"timestamp":"2025-06-20T12:35:54.831819Z"} 
[2025-06-20 12:35:54] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/auth/login","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"],"x-locale":["zh"]},"body":[],"timestamp":"2025-06-20T12:35:54.837545Z"} 
[2025-06-20 12:35:54] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/auth/login","status_code":422,"duration_ms":1.03,"response_size":252,"timestamp":"2025-06-20T12:35:54.838504Z"} 
[2025-06-20 12:35:54] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/auth/login","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["55"],"content-type":["application/json"],"x-locale":["en"]},"body":{"email":"<EMAIL>","password":"***FILTERED***"},"timestamp":"2025-06-20T12:35:54.844410Z"} 
[2025-06-20 12:35:54] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/auth/login","status_code":422,"duration_ms":1.19,"response_size":184,"timestamp":"2025-06-20T12:35:54.845547Z"} 
[2025-06-20 12:35:54] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/auth/login","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["55"],"content-type":["application/json"],"x-locale":["zh"]},"body":{"email":"<EMAIL>","password":"***FILTERED***"},"timestamp":"2025-06-20T12:35:54.851342Z"} 
[2025-06-20 12:35:54] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/auth/login","status_code":422,"duration_ms":1.09,"response_size":206,"timestamp":"2025-06-20T12:35:54.852376Z"} 
[2025-06-20 12:35:54] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/organisations","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:35:54.867891Z"} 
[2025-06-20 12:35:54] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/organisations","status_code":200,"duration_ms":2.9,"response_size":1956,"timestamp":"2025-06-20T12:35:54.870716Z"} 
[2025-06-20 12:35:54] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/organisations?status=active","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":{"status":"active"},"timestamp":"2025-06-20T12:35:54.890691Z"} 
[2025-06-20 12:35:54] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/organisations?status=active","status_code":200,"duration_ms":2.05,"response_size":1181,"timestamp":"2025-06-20T12:35:54.892676Z"} 
[2025-06-20 12:35:54] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/organisations","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["106"],"content-type":["application/json"]},"body":{"name":"Test Organisation","code":"TEST001","details":{"industry":"Technology"},"remarks":"Test remarks"},"timestamp":"2025-06-20T12:35:54.901508Z"} 
[2025-06-20 12:35:54] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/organisations","status_code":201,"duration_ms":2.64,"response_size":387,"timestamp":"2025-06-20T12:35:54.904080Z"} 
[2025-06-20 12:35:54] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/organisations","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:35:54.912808Z"} 
[2025-06-20 12:35:54] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/organisations","status_code":422,"duration_ms":1.95,"response_size":252,"timestamp":"2025-06-20T12:35:54.914692Z"} 
[2025-06-20 12:35:54] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/organisations","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["47"],"content-type":["application/json"]},"body":{"name":"Test Organisation","code":"DUPLICATE"},"timestamp":"2025-06-20T12:35:54.923846Z"} 
[2025-06-20 12:35:54] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/organisations","status_code":422,"duration_ms":2.03,"response_size":186,"timestamp":"2025-06-20T12:35:54.925814Z"} 
[2025-06-20 12:35:54] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/organisations/1","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:35:54.934946Z"} 
[2025-06-20 12:35:54] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/organisations/1","status_code":200,"duration_ms":1.99,"response_size":474,"timestamp":"2025-06-20T12:35:54.936876Z"} 
[2025-06-20 12:35:54] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/organisations/999","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:35:54.945364Z"} 
[2025-06-20 12:35:54] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/organisations/999","status_code":404,"duration_ms":2.03,"response_size":135,"timestamp":"2025-06-20T12:35:54.947335Z"} 
[2025-06-20 12:35:54] testing.INFO: API Request {"method":"PUT","url":"http://localhost/api/v1/organisations/1","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["63"],"content-type":["application/json"]},"body":{"name":"New Name","code":"NEW001","remarks":"Updated remarks"},"timestamp":"2025-06-20T12:35:54.956127Z"} 
[2025-06-20 12:35:54] testing.INFO: API Response {"method":"PUT","url":"http://localhost/api/v1/organisations/1","status_code":200,"duration_ms":4.2,"response_size":409,"timestamp":"2025-06-20T12:35:54.960266Z"} 
[2025-06-20 12:35:54] testing.INFO: API Request {"method":"PUT","url":"http://localhost/api/v1/organisations/999","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["19"],"content-type":["application/json"]},"body":{"name":"New Name"},"timestamp":"2025-06-20T12:35:54.976078Z"} 
[2025-06-20 12:35:54] testing.INFO: API Response {"method":"PUT","url":"http://localhost/api/v1/organisations/999","status_code":404,"duration_ms":1.38,"response_size":135,"timestamp":"2025-06-20T12:35:54.977393Z"} 
[2025-06-20 12:35:54] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/organisations/1/suspend","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:35:54.986263Z"} 
[2025-06-20 12:35:54] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/organisations/1/suspend","status_code":200,"duration_ms":1.91,"response_size":409,"timestamp":"2025-06-20T12:35:54.988116Z"} 
[2025-06-20 12:35:54] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/organisations/1/suspend","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:35:54.997140Z"} 
[2025-06-20 12:35:54] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/organisations/1/suspend","status_code":422,"duration_ms":1.59,"response_size":165,"timestamp":"2025-06-20T12:35:54.998672Z"} 
[2025-06-20 12:35:55] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/organisations","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:35:55.007912Z"} 
[2025-06-20 12:35:55] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/organisations","status_code":401,"duration_ms":0.68,"response_size":145,"timestamp":"2025-06-20T12:35:55.008526Z"} 
[2025-06-20 12:35:55] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/organisations","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:35:55.009224Z"} 
[2025-06-20 12:35:55] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/organisations","status_code":401,"duration_ms":0.3,"response_size":145,"timestamp":"2025-06-20T12:35:55.009515Z"} 
[2025-06-20 12:35:55] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/organisations/1","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:35:55.010150Z"} 
[2025-06-20 12:35:55] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/organisations/1","status_code":401,"duration_ms":0.27,"response_size":145,"timestamp":"2025-06-20T12:35:55.010418Z"} 
[2025-06-20 12:35:55] testing.INFO: API Request {"method":"PUT","url":"http://localhost/api/v1/organisations/1","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:35:55.011055Z"} 
[2025-06-20 12:35:55] testing.INFO: API Response {"method":"PUT","url":"http://localhost/api/v1/organisations/1","status_code":401,"duration_ms":0.27,"response_size":145,"timestamp":"2025-06-20T12:35:55.011317Z"} 
[2025-06-20 12:35:55] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/organisations/1/suspend","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:35:55.011893Z"} 
[2025-06-20 12:35:55] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/organisations/1/suspend","status_code":401,"duration_ms":0.26,"response_size":145,"timestamp":"2025-06-20T12:35:55.012149Z"} 
[2025-06-20 12:35:55] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/organisations/1","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:35:55.022585Z"} 
[2025-06-20 12:35:55] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/organisations/1","status_code":200,"duration_ms":2.12,"response_size":481,"timestamp":"2025-06-20T12:35:55.024651Z"} 
[2025-06-20 12:35:55] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/organisations/2","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:35:55.035928Z"} 
[2025-06-20 12:35:55] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/organisations/2","status_code":403,"duration_ms":1.85,"response_size":134,"timestamp":"2025-06-20T12:35:55.037716Z"} 
[2025-06-20 12:35:55] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/organisations/1","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:35:55.048123Z"} 
[2025-06-20 12:35:55] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/organisations/1","status_code":403,"duration_ms":1.79,"response_size":134,"timestamp":"2025-06-20T12:35:55.049849Z"} 
[2025-06-20 12:35:55] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:35:55.070699Z"} 
[2025-06-20 12:35:55] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/roles","status_code":200,"duration_ms":1.81,"response_size":417,"timestamp":"2025-06-20T12:35:55.072440Z"} 
[2025-06-20 12:35:55] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:35:55.086316Z"} 
[2025-06-20 12:35:55] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/roles","status_code":200,"duration_ms":1.62,"response_size":417,"timestamp":"2025-06-20T12:35:55.087872Z"} 
[2025-06-20 12:35:55] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:35:55.101175Z"} 
[2025-06-20 12:35:55] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/roles","status_code":200,"duration_ms":1.71,"response_size":407,"timestamp":"2025-06-20T12:35:55.102802Z"} 
[2025-06-20 12:35:55] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:35:55.116319Z"} 
[2025-06-20 12:35:55] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/roles","status_code":200,"duration_ms":1.83,"response_size":407,"timestamp":"2025-06-20T12:35:55.118075Z"} 
[2025-06-20 12:35:55] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:35:55.132167Z"} 
[2025-06-20 12:35:55] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/roles","status_code":403,"duration_ms":0.96,"response_size":142,"timestamp":"2025-06-20T12:35:55.133052Z"} 
[2025-06-20 12:35:55] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:35:55.146462Z"} 
[2025-06-20 12:35:55] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/roles","status_code":403,"duration_ms":0.9,"response_size":142,"timestamp":"2025-06-20T12:35:55.147295Z"} 
[2025-06-20 12:35:55] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:35:55.160515Z"} 
[2025-06-20 12:35:55] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/roles","status_code":401,"duration_ms":0.71,"response_size":145,"timestamp":"2025-06-20T12:35:55.161159Z"} 
[2025-06-20 12:35:55] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:35:55.174552Z"} 
[2025-06-20 12:35:55] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/roles","status_code":403,"duration_ms":0.94,"response_size":142,"timestamp":"2025-06-20T12:35:55.175431Z"} 
[2025-06-20 12:35:55] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:35:55.196313Z"} 
[2025-06-20 12:35:55] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/roles","status_code":200,"duration_ms":1.72,"response_size":417,"timestamp":"2025-06-20T12:35:55.197952Z"} 
[2025-06-20 12:35:55] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:35:55.212156Z"} 
[2025-06-20 12:35:55] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/roles","status_code":200,"duration_ms":1.68,"response_size":417,"timestamp":"2025-06-20T12:35:55.213766Z"} 
[2025-06-20 12:35:55] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:35:55.227142Z"} 
[2025-06-20 12:35:55] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/roles","status_code":403,"duration_ms":0.92,"response_size":142,"timestamp":"2025-06-20T12:35:55.227992Z"} 
[2025-06-20 12:35:55] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:35:55.241451Z"} 
[2025-06-20 12:35:55] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/roles","status_code":200,"duration_ms":1.68,"response_size":407,"timestamp":"2025-06-20T12:35:55.243062Z"} 
[2025-06-20 12:35:55] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["59"],"content-type":["application/json"]},"body":{"name":"test-role","organisation_id":1,"guard_name":"api"},"timestamp":"2025-06-20T12:35:55.256203Z"} 
[2025-06-20 12:35:55] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/roles","status_code":201,"duration_ms":2.3,"response_size":256,"timestamp":"2025-06-20T12:35:55.258444Z"} 
[2025-06-20 12:35:55] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["49"],"content-type":["application/json"]},"body":{"name":"test-system-role","guard_name":"system"},"timestamp":"2025-06-20T12:35:55.272688Z"} 
[2025-06-20 12:35:55] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/roles","status_code":201,"duration_ms":2.13,"response_size":269,"timestamp":"2025-06-20T12:35:55.274746Z"} 
[2025-06-20 12:35:55] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["65"],"content-type":["application/json"]},"body":{"name":"invalid-role","organisation_id":1,"guard_name":"system"},"timestamp":"2025-06-20T12:35:55.288984Z"} 
[2025-06-20 12:35:55] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/roles","status_code":422,"duration_ms":2.68,"response_size":173,"timestamp":"2025-06-20T12:35:55.291608Z"} 
[2025-06-20 12:35:55] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/roles/3","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:35:55.306101Z"} 
[2025-06-20 12:35:55] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/roles/3","status_code":200,"duration_ms":1.5,"response_size":254,"timestamp":"2025-06-20T12:35:55.307483Z"} 
[2025-06-20 12:35:55] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:35:55.320921Z"} 
[2025-06-20 12:35:55] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/roles","status_code":401,"duration_ms":0.75,"response_size":145,"timestamp":"2025-06-20T12:35:55.321596Z"} 
[2025-06-20 12:35:55] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:35:55.335575Z"} 
[2025-06-20 12:35:55] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/roles","status_code":200,"duration_ms":1.65,"response_size":417,"timestamp":"2025-06-20T12:35:55.337153Z"} 
[2025-06-20 12:35:55] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:35:55.350886Z"} 
[2025-06-20 12:35:55] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/roles","status_code":403,"duration_ms":0.88,"response_size":142,"timestamp":"2025-06-20T12:35:55.351696Z"} 
[2025-06-20 12:35:55] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/users","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:35:55.374306Z"} 
[2025-06-20 12:35:55] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/users","status_code":200,"duration_ms":5.74,"response_size":2183,"timestamp":"2025-06-20T12:35:55.379969Z"} 
[2025-06-20 12:35:55] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/users","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:35:55.394389Z"} 
[2025-06-20 12:35:55] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/users","status_code":200,"duration_ms":2.68,"response_size":2194,"timestamp":"2025-06-20T12:35:55.397000Z"} 
[2025-06-20 12:35:55] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/users","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:35:55.410409Z"} 
[2025-06-20 12:35:55] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/users","status_code":200,"duration_ms":2.34,"response_size":763,"timestamp":"2025-06-20T12:35:55.412671Z"} 
[2025-06-20 12:35:55] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/users?organisation_id=1","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":{"organisation_id":"1"},"timestamp":"2025-06-20T12:35:55.425842Z"} 
[2025-06-20 12:35:55] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/users?organisation_id=1","status_code":200,"duration_ms":10.2,"response_size":780,"timestamp":"2025-06-20T12:35:55.436009Z"} 
[2025-06-20 12:35:55] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/users?organisation_id=2","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":{"organisation_id":"2"},"timestamp":"2025-06-20T12:35:55.449894Z"} 
[2025-06-20 12:35:55] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/users?organisation_id=2","status_code":422,"duration_ms":1.51,"response_size":197,"timestamp":"2025-06-20T12:35:55.451335Z"} 
[2025-06-20 12:35:55] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/users","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:35:55.464720Z"} 
[2025-06-20 12:35:55] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/users","status_code":200,"duration_ms":2.55,"response_size":767,"timestamp":"2025-06-20T12:35:55.467203Z"} 
[2025-06-20 12:35:55] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/users","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:35:55.481125Z"} 
[2025-06-20 12:35:55] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/users","status_code":403,"duration_ms":1.09,"response_size":134,"timestamp":"2025-06-20T12:35:55.482144Z"} 
[2025-06-20 12:35:55] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/users/6","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:35:55.495949Z"} 
[2025-06-20 12:35:55] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/users/6","status_code":200,"duration_ms":1.44,"response_size":398,"timestamp":"2025-06-20T12:35:55.497311Z"} 
[2025-06-20 12:35:55] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/users/6","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:35:55.510766Z"} 
[2025-06-20 12:35:55] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/users/6","status_code":200,"duration_ms":1.69,"response_size":402,"timestamp":"2025-06-20T12:35:55.512392Z"} 
[2025-06-20 12:35:55] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/users/7","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:35:55.525951Z"} 
[2025-06-20 12:35:55] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/users/7","status_code":403,"duration_ms":1.36,"response_size":134,"timestamp":"2025-06-20T12:35:55.527251Z"} 
[2025-06-20 12:35:55] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/users/4","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:35:55.541163Z"} 
[2025-06-20 12:35:55] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/users/4","status_code":200,"duration_ms":1.79,"response_size":394,"timestamp":"2025-06-20T12:35:55.542893Z"} 
[2025-06-20 12:35:55] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/users/6","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:35:55.556647Z"} 
[2025-06-20 12:35:55] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/users/6","status_code":403,"duration_ms":1.32,"response_size":134,"timestamp":"2025-06-20T12:35:55.557913Z"} 
[2025-06-20 12:35:55] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["99"],"content-type":["application/json"]},"body":{"name":"New User","email":"<EMAIL>","password":"***FILTERED***","organisation_ids":[1,2]},"timestamp":"2025-06-20T12:35:55.572203Z"} 
[2025-06-20 12:35:55] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users","status_code":201,"duration_ms":3.93,"response_size":426,"timestamp":"2025-06-20T12:35:55.576073Z"} 
[2025-06-20 12:35:55] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["104"],"content-type":["application/json"]},"body":{"name":"New Org User","email":"<EMAIL>","password":"***FILTERED***","organisation_ids":[1]},"timestamp":"2025-06-20T12:35:55.589648Z"} 
[2025-06-20 12:35:55] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users","status_code":201,"duration_ms":3.35,"response_size":363,"timestamp":"2025-06-20T12:35:55.592943Z"} 
[2025-06-20 12:35:55] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["101"],"content-type":["application/json"]},"body":{"name":"Invalid User","email":"<EMAIL>","password":"***FILTERED***","organisation_ids":[2]},"timestamp":"2025-06-20T12:35:55.606824Z"} 
[2025-06-20 12:35:55] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users","status_code":422,"duration_ms":2.02,"response_size":215,"timestamp":"2025-06-20T12:35:55.608779Z"} 
[2025-06-20 12:35:55] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["85"],"content-type":["application/json"]},"body":{"name":"Default Org User","email":"<EMAIL>","password":"***FILTERED***"},"timestamp":"2025-06-20T12:35:55.622642Z"} 
[2025-06-20 12:35:55] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users","status_code":201,"duration_ms":3.36,"response_size":367,"timestamp":"2025-06-20T12:35:55.625908Z"} 
[2025-06-20 12:35:55] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["88"],"content-type":["application/json"]},"body":{"name":"Unauthorized User","email":"<EMAIL>","password":"***FILTERED***"},"timestamp":"2025-06-20T12:35:55.640122Z"} 
[2025-06-20 12:35:55] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users","status_code":403,"duration_ms":1.07,"response_size":134,"timestamp":"2025-06-20T12:35:55.641126Z"} 
[2025-06-20 12:35:55] testing.INFO: API Request {"method":"PUT","url":"http://localhost/api/v1/users/6","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["23"],"content-type":["application/json"]},"body":{"name":"Updated Name"},"timestamp":"2025-06-20T12:35:55.654878Z"} 
[2025-06-20 12:35:55] testing.INFO: API Response {"method":"PUT","url":"http://localhost/api/v1/users/6","status_code":200,"duration_ms":2.66,"response_size":395,"timestamp":"2025-06-20T12:35:55.657481Z"} 
[2025-06-20 12:35:55] testing.INFO: API Request {"method":"PUT","url":"http://localhost/api/v1/users/6","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["30"],"content-type":["application/json"]},"body":{"name":"Updated Member Name"},"timestamp":"2025-06-20T12:35:55.671168Z"} 
[2025-06-20 12:35:55] testing.INFO: API Response {"method":"PUT","url":"http://localhost/api/v1/users/6","status_code":200,"duration_ms":2.28,"response_size":404,"timestamp":"2025-06-20T12:35:55.673393Z"} 
[2025-06-20 12:35:55] testing.INFO: API Request {"method":"PUT","url":"http://localhost/api/v1/users/7","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["28"],"content-type":["application/json"]},"body":{"name":"Should Not Update"},"timestamp":"2025-06-20T12:35:55.687134Z"} 
[2025-06-20 12:35:55] testing.INFO: API Response {"method":"PUT","url":"http://localhost/api/v1/users/7","status_code":403,"duration_ms":1.33,"response_size":134,"timestamp":"2025-06-20T12:35:55.688403Z"} 
[2025-06-20 12:35:55] testing.INFO: API Request {"method":"PUT","url":"http://localhost/api/v1/users/6","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["30"],"content-type":["application/json"]},"body":{"name":"Unauthorized Update"},"timestamp":"2025-06-20T12:35:55.702667Z"} 
[2025-06-20 12:35:55] testing.INFO: API Response {"method":"PUT","url":"http://localhost/api/v1/users/6","status_code":403,"duration_ms":1.43,"response_size":134,"timestamp":"2025-06-20T12:35:55.704016Z"} 
[2025-06-20 12:35:55] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/6/suspend","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:35:55.717759Z"} 
[2025-06-20 12:35:55] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/6/suspend","status_code":200,"duration_ms":1.65,"response_size":390,"timestamp":"2025-06-20T12:35:55.719313Z"} 
[2025-06-20 12:35:55] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/6/suspend","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:35:55.742066Z"} 
[2025-06-20 12:35:55] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/6/suspend","status_code":200,"duration_ms":1.94,"response_size":388,"timestamp":"2025-06-20T12:35:55.743924Z"} 
[2025-06-20 12:35:55] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/7/suspend","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:35:55.757883Z"} 
[2025-06-20 12:35:55] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/7/suspend","status_code":403,"duration_ms":1.37,"response_size":134,"timestamp":"2025-06-20T12:35:55.759186Z"} 
[2025-06-20 12:35:55] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/6/activate","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:35:55.772707Z"} 
[2025-06-20 12:35:55] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/6/activate","status_code":200,"duration_ms":1.53,"response_size":391,"timestamp":"2025-06-20T12:35:55.774179Z"} 
[2025-06-20 12:35:55] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/6/activate","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:35:55.787665Z"} 
[2025-06-20 12:35:55] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/6/activate","status_code":200,"duration_ms":1.73,"response_size":391,"timestamp":"2025-06-20T12:35:55.789344Z"} 
[2025-06-20 12:35:55] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/7/activate","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:35:55.804099Z"} 
[2025-06-20 12:35:55] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/7/activate","status_code":403,"duration_ms":1.33,"response_size":134,"timestamp":"2025-06-20T12:35:55.805364Z"} 
[2025-06-20 12:35:55] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/6/suspend","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:35:55.818875Z"} 
[2025-06-20 12:35:55] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/6/suspend","status_code":403,"duration_ms":1.28,"response_size":134,"timestamp":"2025-06-20T12:35:55.820096Z"} 
[2025-06-20 12:35:55] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/6/activate","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:35:55.833809Z"} 
[2025-06-20 12:35:55] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/6/activate","status_code":403,"duration_ms":1.29,"response_size":134,"timestamp":"2025-06-20T12:35:55.835043Z"} 
[2025-06-20 12:35:55] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/users","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:35:55.856608Z"} 
[2025-06-20 12:35:55] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/users","status_code":200,"duration_ms":3.09,"response_size":1206,"timestamp":"2025-06-20T12:35:55.859623Z"} 
[2025-06-20 12:35:55] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/users?organisation_id=1","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":{"organisation_id":"1"},"timestamp":"2025-06-20T12:35:55.871369Z"} 
[2025-06-20 12:35:55] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/users?organisation_id=1","status_code":200,"duration_ms":2.86,"response_size":844,"timestamp":"2025-06-20T12:35:55.874162Z"} 
[2025-06-20 12:35:55] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["99"],"content-type":["application/json"]},"body":{"name":"New User","email":"<EMAIL>","password":"***FILTERED***","organisation_ids":[1,2]},"timestamp":"2025-06-20T12:35:55.883365Z"} 
[2025-06-20 12:35:55] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users","status_code":201,"duration_ms":3.82,"response_size":435,"timestamp":"2025-06-20T12:35:55.887134Z"} 
[2025-06-20 12:35:55] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["76"],"content-type":["application/json"]},"body":{"name":"Simple User","email":"<EMAIL>","password":"***FILTERED***"},"timestamp":"2025-06-20T12:35:55.896735Z"} 
[2025-06-20 12:35:55] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users","status_code":201,"duration_ms":3.6,"response_size":292,"timestamp":"2025-06-20T12:35:55.900259Z"} 
[2025-06-20 12:35:55] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/register","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["103"],"content-type":["application/json"]},"body":{"name":"Guest User","email":"<EMAIL>","password":"***FILTERED***","verification_code":"185668"},"timestamp":"2025-06-20T12:35:55.910186Z"} 
[2025-06-20 12:35:55] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/register","status_code":201,"duration_ms":11.41,"response_size":293,"timestamp":"2025-06-20T12:35:55.921544Z"} 
[2025-06-20 12:35:55] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/register","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["103"],"content-type":["application/json"]},"body":{"name":"Guest User","email":"<EMAIL>","password":"***FILTERED***","verification_code":"123456"},"timestamp":"2025-06-20T12:35:55.931478Z"} 
[2025-06-20 12:35:55] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/register","status_code":422,"duration_ms":1.33,"response_size":194,"timestamp":"2025-06-20T12:35:55.932751Z"} 
[2025-06-20 12:35:55] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/register","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["74"],"content-type":["application/json"]},"body":{"name":"Guest User","email":"<EMAIL>","password":"***FILTERED***"},"timestamp":"2025-06-20T12:35:55.941895Z"} 
[2025-06-20 12:35:55] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/register","status_code":422,"duration_ms":1.36,"response_size":186,"timestamp":"2025-06-20T12:35:55.943195Z"} 
[2025-06-20 12:35:55] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/register","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["126"],"content-type":["application/json"]},"body":{"name":"Guest User","email":"<EMAIL>","password":"***FILTERED***","verification_code":"864592","organisation_ids":[1]},"timestamp":"2025-06-20T12:35:55.961095Z"} 
[2025-06-20 12:35:55] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/register","status_code":422,"duration_ms":1.15,"response_size":218,"timestamp":"2025-06-20T12:35:55.962197Z"} 
[2025-06-20 12:35:55] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:35:55.971393Z"} 
[2025-06-20 12:35:55] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users","status_code":422,"duration_ms":1.88,"response_size":305,"timestamp":"2025-06-20T12:35:55.973213Z"} 
[2025-06-20 12:35:55] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["77"],"content-type":["application/json"]},"body":{"name":"Test User","email":"<EMAIL>","password":"***FILTERED***"},"timestamp":"2025-06-20T12:35:55.982819Z"} 
[2025-06-20 12:35:55] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users","status_code":422,"duration_ms":2.05,"response_size":199,"timestamp":"2025-06-20T12:35:55.984809Z"} 
[2025-06-20 12:35:55] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["102"],"content-type":["application/json"]},"body":{"name":"Test User","email":"<EMAIL>","password":"***FILTERED***","organisation_ids":[999,1000]},"timestamp":"2025-06-20T12:35:55.994078Z"} 
[2025-06-20 12:35:55] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users","status_code":422,"duration_ms":2.57,"response_size":280,"timestamp":"2025-06-20T12:35:55.996569Z"} 
[2025-06-20 12:35:56] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/users/3","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:35:56.007189Z"} 
[2025-06-20 12:35:56] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/users/3","status_code":200,"duration_ms":3.82,"response_size":468,"timestamp":"2025-06-20T12:35:56.010890Z"} 
[2025-06-20 12:35:56] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/users/999","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:35:56.024696Z"} 
[2025-06-20 12:35:56] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/users/999","status_code":404,"duration_ms":1.68,"response_size":135,"timestamp":"2025-06-20T12:35:56.026289Z"} 
[2025-06-20 12:35:56] testing.INFO: API Request {"method":"PUT","url":"http://localhost/api/v1/users/3","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["42"],"content-type":["application/json"]},"body":{"name":"New Name","organisation_ids":[2]},"timestamp":"2025-06-20T12:35:56.036851Z"} 
[2025-06-20 12:35:56] testing.INFO: API Response {"method":"PUT","url":"http://localhost/api/v1/users/3","status_code":200,"duration_ms":4.31,"response_size":383,"timestamp":"2025-06-20T12:35:56.041095Z"} 
[2025-06-20 12:35:56] testing.INFO: API Request {"method":"PUT","url":"http://localhost/api/v1/users/999","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["19"],"content-type":["application/json"]},"body":{"name":"New Name"},"timestamp":"2025-06-20T12:35:56.050743Z"} 
[2025-06-20 12:35:56] testing.INFO: API Response {"method":"PUT","url":"http://localhost/api/v1/users/999","status_code":404,"duration_ms":1.39,"response_size":135,"timestamp":"2025-06-20T12:35:56.052066Z"} 
[2025-06-20 12:35:56] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/3/organisations/1","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:35:56.061729Z"} 
[2025-06-20 12:35:56] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/3/organisations/1","status_code":200,"duration_ms":2.43,"response_size":399,"timestamp":"2025-06-20T12:35:56.064091Z"} 
[2025-06-20 12:35:56] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/3/organisations/1","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:35:56.075296Z"} 
[2025-06-20 12:35:56] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/3/organisations/1","status_code":200,"duration_ms":2.43,"response_size":398,"timestamp":"2025-06-20T12:35:56.077686Z"} 
[2025-06-20 12:35:56] testing.INFO: API Request {"method":"DELETE","url":"http://localhost/api/v1/users/3/organisations/1","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:35:56.088305Z"} 
[2025-06-20 12:35:56] testing.INFO: API Response {"method":"DELETE","url":"http://localhost/api/v1/users/3/organisations/1","status_code":200,"duration_ms":3.16,"response_size":410,"timestamp":"2025-06-20T12:35:56.091400Z"} 
[2025-06-20 12:35:56] testing.INFO: API Request {"method":"PUT","url":"http://localhost/api/v1/users/3/organisations","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["24"],"content-type":["application/json"]},"body":{"organisation_ids":[2]},"timestamp":"2025-06-20T12:35:56.102145Z"} 
[2025-06-20 12:35:56] testing.INFO: API Response {"method":"PUT","url":"http://localhost/api/v1/users/3/organisations","status_code":200,"duration_ms":3.67,"response_size":421,"timestamp":"2025-06-20T12:35:56.105742Z"} 
[2025-06-20 12:35:56] testing.INFO: API Request {"method":"PUT","url":"http://localhost/api/v1/users/3/organisations","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["23"],"content-type":["application/json"]},"body":{"organisation_ids":[]},"timestamp":"2025-06-20T12:35:56.116012Z"} 
[2025-06-20 12:35:56] testing.INFO: API Response {"method":"PUT","url":"http://localhost/api/v1/users/3/organisations","status_code":200,"duration_ms":3.92,"response_size":339,"timestamp":"2025-06-20T12:35:56.119873Z"} 
[2025-06-20 12:35:56] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/3/suspend","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:35:56.130313Z"} 
[2025-06-20 12:35:56] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/3/suspend","status_code":200,"duration_ms":2.06,"response_size":322,"timestamp":"2025-06-20T12:35:56.132304Z"} 
[2025-06-20 12:35:56] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/users","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:35:56.142173Z"} 
[2025-06-20 12:35:56] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/users","status_code":401,"duration_ms":0.68,"response_size":145,"timestamp":"2025-06-20T12:35:56.142787Z"} 
[2025-06-20 12:35:56] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:35:56.143504Z"} 
[2025-06-20 12:35:56] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users","status_code":401,"duration_ms":0.35,"response_size":145,"timestamp":"2025-06-20T12:35:56.143845Z"} 
[2025-06-20 12:35:56] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/users/3","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:35:56.144458Z"} 
[2025-06-20 12:35:56] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/users/3","status_code":401,"duration_ms":0.29,"response_size":145,"timestamp":"2025-06-20T12:35:56.144745Z"} 
[2025-06-20 12:35:56] testing.INFO: API Request {"method":"PUT","url":"http://localhost/api/v1/users/3","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:35:56.145338Z"} 
[2025-06-20 12:35:56] testing.INFO: API Response {"method":"PUT","url":"http://localhost/api/v1/users/3","status_code":401,"duration_ms":0.26,"response_size":145,"timestamp":"2025-06-20T12:35:56.145598Z"} 
[2025-06-20 12:35:56] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/3/suspend","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:35:56.146189Z"} 
[2025-06-20 12:35:56] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/3/suspend","status_code":401,"duration_ms":0.26,"response_size":145,"timestamp":"2025-06-20T12:35:56.146444Z"} 
[2025-06-20 12:35:56] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/3/organisations/1","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:35:56.147046Z"} 
[2025-06-20 12:35:56] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/3/organisations/1","status_code":401,"duration_ms":0.26,"response_size":145,"timestamp":"2025-06-20T12:35:56.147298Z"} 
[2025-06-20 12:35:56] testing.INFO: API Request {"method":"DELETE","url":"http://localhost/api/v1/users/3/organisations/1","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:35:56.147881Z"} 
[2025-06-20 12:35:56] testing.INFO: API Response {"method":"DELETE","url":"http://localhost/api/v1/users/3/organisations/1","status_code":401,"duration_ms":0.25,"response_size":145,"timestamp":"2025-06-20T12:35:56.148130Z"} 
[2025-06-20 12:35:56] testing.INFO: API Request {"method":"PUT","url":"http://localhost/api/v1/users/3/organisations","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:35:56.148696Z"} 
[2025-06-20 12:35:56] testing.INFO: API Response {"method":"PUT","url":"http://localhost/api/v1/users/3/organisations","status_code":401,"duration_ms":0.25,"response_size":145,"timestamp":"2025-06-20T12:35:56.148944Z"} 
[2025-06-20 12:35:56] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/send-verification-code","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["28"],"content-type":["application/json"]},"body":{"email":"<EMAIL>"},"timestamp":"2025-06-20T12:35:56.158349Z"} 
[2025-06-20 12:35:56] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/send-verification-code","status_code":200,"duration_ms":1.26,"response_size":197,"timestamp":"2025-06-20T12:35:56.159547Z"} 
[2025-06-20 12:35:56] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/send-verification-code","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["25"],"content-type":["application/json"]},"body":{"email":"invalid-email"},"timestamp":"2025-06-20T12:35:56.168623Z"} 
[2025-06-20 12:35:56] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/send-verification-code","status_code":422,"duration_ms":1.01,"response_size":176,"timestamp":"2025-06-20T12:35:56.169570Z"} 
[2025-06-20 12:35:56] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/send-verification-code","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["32"],"content-type":["application/json"]},"body":{"email":"<EMAIL>"},"timestamp":"2025-06-20T12:35:56.178708Z"} 
[2025-06-20 12:35:56] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/send-verification-code","status_code":422,"duration_ms":9.26,"response_size":181,"timestamp":"2025-06-20T12:35:56.187909Z"} 
[2025-06-20 12:35:56] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/send-verification-code","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["28"],"content-type":["application/json"]},"body":{"email":"<EMAIL>"},"timestamp":"2025-06-20T12:35:56.197648Z"} 
[2025-06-20 12:35:56] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/send-verification-code","status_code":422,"duration_ms":0.85,"response_size":185,"timestamp":"2025-06-20T12:35:56.198444Z"} 
[2025-06-20 12:35:56] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/6/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["13"],"content-type":["application/json"]},"body":{"role_id":4},"timestamp":"2025-06-20T12:35:56.220949Z"} 
[2025-06-20 12:35:56] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/6/roles","status_code":200,"duration_ms":2.4,"response_size":170,"timestamp":"2025-06-20T12:35:56.223267Z"} 
[2025-06-20 12:35:56] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/6/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["13"],"content-type":["application/json"]},"body":{"role_id":4},"timestamp":"2025-06-20T12:35:56.236220Z"} 
[2025-06-20 12:35:56] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/6/roles","status_code":200,"duration_ms":2.42,"response_size":170,"timestamp":"2025-06-20T12:35:56.238565Z"} 
[2025-06-20 12:35:56] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/6/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["13"],"content-type":["application/json"]},"body":{"role_id":4},"timestamp":"2025-06-20T12:35:56.250598Z"} 
[2025-06-20 12:35:56] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/6/roles","status_code":200,"duration_ms":2.95,"response_size":170,"timestamp":"2025-06-20T12:35:56.253483Z"} 
[2025-06-20 12:35:56] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/6/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["13"],"content-type":["application/json"]},"body":{"role_id":3},"timestamp":"2025-06-20T12:35:56.265728Z"} 
[2025-06-20 12:35:56] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/6/roles","status_code":422,"duration_ms":2.21,"response_size":281,"timestamp":"2025-06-20T12:35:56.267873Z"} 
[2025-06-20 12:35:56] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/6/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["13"],"content-type":["application/json"]},"body":{"role_id":4},"timestamp":"2025-06-20T12:35:56.280395Z"} 
[2025-06-20 12:35:56] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/6/roles","status_code":403,"duration_ms":2.48,"response_size":134,"timestamp":"2025-06-20T12:35:56.282794Z"} 
[2025-06-20 12:35:56] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/6/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["13"],"content-type":["application/json"]},"body":{"role_id":3},"timestamp":"2025-06-20T12:35:56.295500Z"} 
[2025-06-20 12:35:56] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/6/roles","status_code":422,"duration_ms":2.3,"response_size":281,"timestamp":"2025-06-20T12:35:56.297729Z"} 
[2025-06-20 12:35:56] testing.INFO: API Request {"method":"PUT","url":"http://localhost/api/v1/users/6/transfer-owner","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["21"],"content-type":["application/json"]},"body":{"organisation_id":1},"timestamp":"2025-06-20T12:35:56.310633Z"} 
[2025-06-20 12:35:56] testing.INFO: API Response {"method":"PUT","url":"http://localhost/api/v1/users/6/transfer-owner","status_code":200,"duration_ms":3.37,"response_size":174,"timestamp":"2025-06-20T12:35:56.313930Z"} 
[2025-06-20 12:35:56] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/user","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:35:56.326736Z"} 
[2025-06-20 12:35:56] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/user","status_code":200,"duration_ms":1.5,"response_size":551,"timestamp":"2025-06-20T12:35:56.328175Z"} 
[2025-06-20 12:35:56] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/users/assignable-roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:35:56.340400Z"} 
[2025-06-20 12:35:56] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/users/assignable-roles","status_code":200,"duration_ms":1.84,"response_size":142,"timestamp":"2025-06-20T12:35:56.342171Z"} 
[2025-06-20 12:35:56] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/health","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:35:56.352889Z"} 
[2025-06-20 12:35:56] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/health","status_code":200,"duration_ms":11.52,"response_size":263,"timestamp":"2025-06-20T12:35:56.364328Z"} 
[2025-06-20 12:35:56] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/status","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:35:56.371396Z"} 
[2025-06-20 12:35:56] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/status","status_code":200,"duration_ms":0.6,"response_size":388,"timestamp":"2025-06-20T12:35:56.371924Z"} 
[2025-06-20 12:35:56] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/health","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:35:56.377756Z"} 
[2025-06-20 12:35:56] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/health","status_code":200,"duration_ms":1.07,"response_size":263,"timestamp":"2025-06-20T12:35:56.378768Z"} 
[2025-06-20 12:35:56] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/status","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:35:56.391019Z"} 
[2025-06-20 12:35:56] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/status","status_code":200,"duration_ms":0.62,"response_size":388,"timestamp":"2025-06-20T12:35:56.391581Z"} 
[2025-06-20 12:35:56] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/status","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T12:35:56.397958Z"} 
[2025-06-20 12:35:56] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/status","status_code":200,"duration_ms":0.62,"response_size":388,"timestamp":"2025-06-20T12:35:56.398516Z"} 
